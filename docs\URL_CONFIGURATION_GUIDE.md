# URL配置使用指南

本文档说明如何在项目工时API中正确使用配置文件的base_url。

## 🔧 修改内容总结

### 1. **修复了project_hour_api.py中的URL配置问题**

#### 修改前的问题：
- ❌ 硬编码URL: `url = "http://rpm-stress.phc-ts.local/rpm-api/project/cardList"`
- ❌ 未定义变量: `url = base_url + "/#/login"`
- ❌ 硬编码项目ID: `projectId=1930148914887311362`

#### 修改后的解决方案：

**1. 页面登录方法 (login_user)**
```python
@allure.step("用户登录")
def login_user(self, payload, headers):
    """登录获取token"""
    # 从配置文件获取base_url
    base_url = self.config.get("base_url")
    url = f"{base_url}/#/login"
    # ...
```

**2. API登录方法 (api_login_user) - 新增**
```python
@allure.step("API用户登录")
def api_login_user(self, payload, headers):
    """通过API接口登录获取token"""
    url = "/rpm-api/auth/login"  # 相对路径，由RequestUtil自动拼接base_url
    # ...
```

**3. 项目卡片列表查询 (get_project_card_list)**
```python
@allure.step("查询项目管理卡片视图")
def get_project_card_list(self, payload, headers):
    """查询项目列表卡片视图"""
    url = "/rpm-api/project/cardList"  # 相对路径，正确做法
    # ...
```

**4. 项目信息查询 (get_project_info)**
```python
@allure.step("访问项目概览-基本信息")
def get_project_info(self, project_id, headers, page_size=99999):
    """访问项目概览-基本信息"""
    import time
    timestamp = int(time.time() * 1000)
    # 使用传入的project_id参数，而不是硬编码
    url = f"/rpm-api/project/projectInfo?projectId={project_id}&pageSize={page_size}&_t={timestamp}"
    # ...
```

## 📋 URL配置的最佳实践

### 1. **使用相对路径（推荐）**
```python
# ✅ 正确做法 - 使用相对路径
url = "/rpm-api/project/cardList"
response = self.req.send_request("POST", url, json=payload, headers=headers)
```

**优点：**
- RequestUtil会自动拼接配置文件中的base_url
- 代码简洁，易于维护
- 环境切换时无需修改代码

### 2. **特殊路径访问（正确做法）**
```python
# ✅ 正确做法 - 访问特殊路径，仍使用相对路径
url = "/#/login"  # RequestUtil会自动拼接为: base_url + "/#/login"
response = self.req.send_request("POST", url, json=payload, headers=headers)
```

**重要说明：**
- RequestUtil总是会将传入的URL与base_url拼接
- 即使是特殊路径，也应该只传入相对路径部分
- 不要手动拼接完整URL，会导致重复拼接问题

### 3. **避免的错误做法**

#### ❌ **错误1：硬编码完整URL**
```python
url = "http://rpm-stress.phc-ts.local/rpm-api/project/cardList"
```

#### ❌ **错误2：手动拼接完整URL**
```python
base_url = self.config.get("base_url")
url = f"{base_url}/#/login"  # 会导致重复拼接！
```

**问题分析：**
- RequestUtil会自动拼接：`base_url + url`
- 如果传入完整URL，结果会是：`base_url + base_url + path`
- 实际请求URL变成：`http://rpm-stress.phc-ts.local/http://rpm-stress.phc-ts.local/#/login`

**常见错误日志：**
```
POST http://rpm-stress.phc-ts.local/http://rpm-stress.phc-ts.local/#/login
```

## 🔄 配置文件中的base_url

### 当前配置 (config/config.yaml)
```yaml
# 基础配置
base_url: "http://rpm-stress.phc-ts.local"
```

### 环境变量覆盖
```bash
# 可以通过环境变量覆盖
export BASE_URL="https://your-new-url.com"
```

### 多环境配置
```yaml
# config/config_stress.yaml
base_url: "http://rpm-stress.phc-ts.local"

# config/config_uat.yaml  
base_url: "https://rpm-uat.pharmaronclinical.com"

# config/config_prod.yaml
base_url: "https://rpm.pharmaronclinical.com"
```

## 🧪 测试用例更新

### 更新了test_user_login测试
```python
@allure.story("用户登录")
def test_user_login(self, req, config):
    """测试用户登录功能"""
    with allure.step("准备登录数据"):
        auth_config = config.get_auth_config()
        payload = {
            "username": auth_config["username"],
            "password": auth_config["password"],
            "captcha": auth_config["captcha"],
            "checkKey": auth_config.get("checkKey", "")  # 添加checkKey支持
        }
    
    with allure.step("执行API用户登录"):
        project_api = ProjectAPI(req)
        # 使用新的api_login_user方法
        response = project_api.api_login_user(payload, headers)
```

## 🚀 使用方法

### 1. **在API类中获取base_url**
```python
class ProjectAPI:
    def __init__(self, request_util):
        self.req = request_util
        self.config = ConfigManager()
    
    def some_method(self):
        # 方法1：使用相对路径（推荐）
        url = "/rpm-api/some/endpoint"
        
        # 方法2：手动拼接（特殊情况）
        base_url = self.config.get("base_url")
        url = f"{base_url}/special/path"
```

### 2. **在测试用例中验证URL**
```python
def test_api_call(self, req, config):
    """测试API调用"""
    # 获取配置的base_url用于验证
    expected_base_url = config.get("base_url")
    
    # 执行API调用
    project_api = ProjectAPI(req)
    response = project_api.get_project_card_list(payload, headers)
    
    # 验证请求使用了正确的base_url
    # (RequestUtil会在日志中记录完整URL)
```

## 🔍 调试和验证

### 1. **查看日志确认URL**
```
2025-07-28 11:21:00 - common.logger - INFO - [e3d5fbae] 发送请求 - POST http://rpm-stress.phc-ts.local/rpm-api/auth/login
```

### 2. **配置验证**
```python
# 在测试中验证配置
def test_config_validation(self, config):
    base_url = config.get("base_url")
    assert base_url is not None, "base_url配置不能为空"
    assert base_url.startswith("http"), "base_url必须是有效的URL"
```

## 📝 注意事项

1. **相对路径自动拼接**: RequestUtil会自动将相对路径与base_url拼接
2. **环境变量优先级**: 环境变量 > 环境配置文件 > 主配置文件
3. **URL格式**: base_url不应以"/"结尾，相对路径应以"/"开头
4. **特殊字符**: URL中的特殊字符会被自动编码
5. **HTTPS支持**: 框架支持HTTP和HTTPS协议

## 🎯 最佳实践总结

1. ✅ **优先使用相对路径**: `/rpm-api/endpoint`
2. ✅ **从配置获取base_url**: `self.config.get("base_url")`
3. ✅ **使用参数化**: 避免硬编码项目ID等参数
4. ✅ **环境变量覆盖**: 支持不同环境的灵活配置
5. ❌ **避免硬编码**: 不要写死完整URL
6. ❌ **避免重复拼接**: RequestUtil已经处理了URL拼接
