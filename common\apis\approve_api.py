"""
审批管理相关API封装
"""
import allure
import time
from typing import Dict, Any
from ..http.request_util import RequestUtil
from ..core.logger import Logger

logger = Logger().get_logger()

class ApproveAPI:
    """审批管理API封装类"""
    
    def __init__(self, request_util):
        self.req = request_util

    @allure.step("获取审批列表")
    def get_approve_list(self, project_id: str, approve_type: str, page_index: int = 1, page_size: int = 20, headers: Dict[str, Any] = None):
        """获取审批列表
        
        Args:
            project_id: 项目ID
            approve_type: 审批类型
                - visit_published: 发布访视方案审批
                - project_milestone_setting_update: 项目里程碑配置修改审批
                - project_milestone_date_update: 项目里程碑完成日期修改审批
                - center_milestone_setting_update: 中心里程碑配置修改审批
                - center_milestone_date_update: 中心里程碑完成日期修改审批
                - subject_delete: 删除受试者审批
                - subject_update: 编辑受试者审批
                - change_visit: 切换访视方案
                - project_work_hours_visit_crc: CRC项目访视工时审批
                - project_work_hours_novisit_crc: CRC项目非访视工时审批
                - staff_config_crc: 项目人员审批
                - project_close: 项目关闭审批
            page_index: 页码，默认1
            page_size: 每页大小，默认20
            headers: 请求头
            
        Returns:
            response: HTTP响应对象
        """
        url = "/rpm-api/projectApprove/list"
        params = {
            "pageIndex": page_index,
            "pageSize": page_size,
            "projectId": project_id,
            "approveType": approve_type,
            "_t": int(time.time() * 1000)
        }
        
        logger.info(f"获取审批列表请求: project_id={project_id}, approve_type={approve_type}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"获取审批列表响应: {response.status_code} - {response.text}")
        return response

    @allure.step("获取审批详情")
    def get_approve_detail(self, instance_id: str, headers: Dict[str, Any] = None):
        """获取审批详情信息
        
        Args:
            instance_id: 审批实例ID，取自审批列表接口的data.record[0].instanceId
            headers: 请求头
            
        Returns:
            response: HTTP响应对象
        """
        url = "/rpm-api/projectApprove/detail"
        params = {
            "instanceId": instance_id,
            "_t": int(time.time() * 1000)
        }
        
        logger.info(f"获取审批详情请求: instance_id={instance_id}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"获取审批详情响应: {response.status_code} - {response.text}")
        return response

    @allure.step("提交审批")
    def submit_approve(self, step_id: str, instance_id: str, route_id: str, task_user_id: str, 
                      approved_option_txt: str = "审批意见", cc_users: list = None, headers: Dict[str, Any] = None):
        """提交审批
        
        Args:
            step_id: 步骤ID，取自审批详情接口的data.stepId
            instance_id: 实例ID，取自审批详情接口的data.instanceId
            route_id: 路由ID，取自审批详情接口的data.routes[0].id (0为通过，1为驳回)
            task_user_id: 任务用户ID，取自审批详情接口的data.taskUserId
            approved_option_txt: 审批意见，默认"审批意见"
            cc_users: 抄送人列表，可不传
            headers: 请求头
            
        Returns:
            response: HTTP响应对象
        """
        url = "/rpm-api/projectApprove/submit"
        
        if cc_users is None:
            cc_users = []
            
        payload = {
            "stepId": step_id,
            "instanceId": instance_id,
            "routeId": route_id,
            "taskUserId": task_user_id,
            "approvedOptionTxt": approved_option_txt,
            "ccUsers": cc_users
        }
        
        logger.info(f"提交审批请求: instance_id={instance_id}, route_id={route_id}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"提交审批响应: {response.status_code} - {response.text}")
        return response

    def extract_route_ids(self, routes: list) -> tuple:
        """
        从routes中提取通过和驳回的路由ID
        
        Args:
            routes: routes列表，来自审批详情接口
            
        Returns:
            (pass_route_id, reject_route_id) 元组
        """
        if not routes or len(routes) < 2:
            # 如果routes不足2个，使用默认值
            return ("26", "27")
        
        try:
            # routes[0]为通过，routes[1]为驳回
            pass_route_id = str(routes[0].get('id', '26'))
            reject_route_id = str(routes[1].get('id', '27'))
            return (pass_route_id, reject_route_id)
        except (IndexError, KeyError):
            return ("26", "27")

    @allure.step("审批通过")
    def approve_pass(self, step_id: str, instance_id: str, task_user_id: str, 
                    routes: list = None, approved_option_txt: str = "审批意见", 
                    cc_users: list = None, headers: Dict[str, Any] = None):
        """审批通过（快捷方法）
        
        Args:
            step_id: 步骤ID
            instance_id: 实例ID
            task_user_id: 任务用户ID
            routes: routes列表，来自审批详情接口，用于获取正确的路由ID
            approved_option_txt: 审批意见
            cc_users: 抄送人列表
            headers: 请求头
            
        Returns:
            response: HTTP响应对象
        """
        pass_route_id, _ = self.extract_route_ids(routes or [])
        return self.submit_approve(
            step_id=step_id,
            instance_id=instance_id,
            route_id=pass_route_id,
            task_user_id=task_user_id,
            approved_option_txt=approved_option_txt,
            cc_users=cc_users,
            headers=headers
        )

    @allure.step("审批驳回")
    def approve_reject(self, step_id: str, instance_id: str, task_user_id: str, 
                      routes: list = None, approved_option_txt: str = "审批意见", 
                      cc_users: list = None, headers: Dict[str, Any] = None):
        """审批驳回（快捷方法）
        
        Args:
            step_id: 步骤ID
            instance_id: 实例ID
            task_user_id: 任务用户ID
            routes: routes列表，来自审批详情接口，用于获取正确的路由ID
            approved_option_txt: 审批意见
            cc_users: 抄送人列表
            headers: 请求头
            
        Returns:
            response: HTTP响应对象
        """
        _, reject_route_id = self.extract_route_ids(routes or [])
        return self.submit_approve(
            step_id=step_id,
            instance_id=instance_id,
            route_id=reject_route_id,
            task_user_id=task_user_id,
            approved_option_txt=approved_option_txt,
            cc_users=cc_users,
            headers=headers
        )