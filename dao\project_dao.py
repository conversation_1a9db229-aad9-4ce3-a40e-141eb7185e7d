"""
项目DAO - 项目数据访问对象
"""
from typing import List, Optional, Dict, Any
from .base_dao import SQLAlchemyDAO, PyMySQLDAO
from models.project import Project

class ProjectDAO(SQLAlchemyDAO):
    """项目DAO - SQLAlchemy实现"""
    
    @property
    def table_name(self) -> str:
        return "project"
    
    @property
    def model_class(self):
        return Project
    
    def find_by_name(self, name: str) -> Optional[Project]:
        """根据项目名查找项目"""
        return self.query(name=name)
    
    def find_active_projects(self) -> List[Project]:
        """查找所有活跃项目"""
        return self.query(status='active')

class ProjectPyMySQLDAO(PyMySQLDAO):
    """项目DAO - PyMySQL实现"""
    
    @property
    def table_name(self) -> str:
        return "project"
    
    @property
    def model_class(self):
        return Project
    
    def find_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据项目名查找项目"""
        sql = f"SELECT * FROM {self.table_name} WHERE name = %s AND deleted = 0"
        results = self.db_util.execute_query(sql, (name,))
        return results[0] if results else None
    
    def find_active_projects(self) -> List[Dict[str, Any]]:
        """查找所有活跃项目"""
        sql = f"SELECT * FROM {self.table_name} WHERE status = 'active' AND deleted = 0"
        return self.db_util.execute_query(sql)
