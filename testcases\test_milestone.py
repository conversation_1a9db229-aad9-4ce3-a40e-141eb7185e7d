"""
里程碑配置API接口测试用例
"""
import allure

from common.apis.milestone_api import MilestoneApi
from common.http.assert_util import AssertUtil

@allure.feature("里程碑配置API接口")
class TestMilestoneAPI:
    @allure.story("获取项目里程碑目录")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_project_milestone(self, req, headers):
        """获取项目里程碑目录"""
        milestone_api = MilestoneApi(req)
        with allure.step("获取项目里程碑目录"):
            response = milestone_api.get_project_milestone(headers)

        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)    

            response_data = response.json()

            # 验证响应结构
            arr = []

            arr.extend(["childList","name","code"])
            for field in arr:
                assert field in response_data["data"], f"响应中缺少{field}字段"

            assert response_data["message"] == "请求成功", "响应消息不正确"
    @allure.story("获取项目里程碑配置列表")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_project_milestone_config(self, req, headers,subject_data):
        """获取项目里程碑配置列表"""
        project_id = subject_data["project_config"]["default_project_id"]
        milestone_api = MilestoneApi(req)
        with allure.step("获取项目里程碑配置列表"):
            response = milestone_api.get_project_milestone_config(project_id,headers)
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)    

            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确"
    @allure.story("获取中心里程碑目录")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_center_milestone(self, req, headers):
        """获取中心里程碑目录"""
        milestone_api = MilestoneApi(req)
        with allure.step("获取中心里程碑目录"):
            response = milestone_api.get_center_milestone(headers)

        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)    

            response_data = response.json()

            # 验证响应结构
            arr = []

            arr.extend(["childList","name","code"])
            for field in arr:
                assert field in response_data["data"], f"响应中缺少{field}字段"

            assert response_data["message"] == "请求成功", "响应消息不正确"
    @allure.story("获取中心里程碑配置列表")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_center_milestone_config(self, req, headers,subject_data):
        """获取中心里程碑配置列表"""
        project_id = subject_data["project_config"]["default_project_id"]
        milestone_api = MilestoneApi(req)
        with allure.step("获取中心里程碑配置列表"):
            response = milestone_api.get_center_milestone_config(project_id,headers)
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)    

            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确"
    @allure.story("保存受试者随访进度并获取")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_save_subject_schedule(self, req, headers,subject_data):
        """保存受试者随访进度"""
        project_id = subject_data["project_config"]["default_project_id"]
        milestone_api = MilestoneApi(req)
        with allure.step("保存受试者随访进度"):
            response = milestone_api.save_subject_schedule(project_id,headers)
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)    

            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确"
        """获取受试者随访进度"""
        with allure.step("获取受试者随访进度"):
            response = milestone_api.get_subject_schedule(project_id,headers)
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)    

            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确" 
            for item in response_data["data"]:
                if item["statusDocName"] == "筛选失败":
                    assert item["enabled"] == 0, "响应消息不正确"   












