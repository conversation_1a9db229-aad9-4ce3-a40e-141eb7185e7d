---
type: "manual"
---

# RPM自动化测试框架 - Augment Rules

## 项目概述
这是一个基于pytest + requests + allure的接口自动化测试框架，用于RPM系统的API测试。

## 代码风格和约定

### Python代码规范
- 使用4个空格缩进，不使用Tab
- 类名使用PascalCase（如：`RequestUtil`, `ContractAPI`）
- 函数和变量名使用snake_case（如：`send_request`, `test_data`）
- 常量使用UPPER_CASE（如：`MAX_RETRIES`, `DEFAULT_TIMEOUT`）
- 文件名使用snake_case（如：`request_util.py`, `test_contract.py`）

### 导入顺序
```python
# 1. 标准库
import os
import time
import json

# 2. 第三方库
import pytest
import requests
import allure
import yaml

# 3. 本地模块
from common.request_util import RequestUtil
from common.logger import Logger
```

### 注释和文档
- 所有类和公共方法必须有中文docstring
- 使用allure.step装饰器为测试步骤添加中文描述
- 复杂逻辑添加行内注释说明

## 测试用例编写规范

### 测试类和方法命名
- 测试类：`TestXxx`格式（如：`TestContract`, `TestProject`）
- 测试方法：`test_xxx`格式，使用中文描述性名称
- 使用allure装饰器标记功能模块和严重级别

### 测试用例结构
```python
@allure.feature("功能模块名")
class TestXxx:
    
    @allure.story("具体功能点")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_xxx(self, req, headers):
        with allure.step("准备测试数据"):
            # 数据准备
            
        with allure.step("执行API调用"):
            # API调用
            
        with allure.step("验证响应结果"):
            # 断言验证
```

### Fixture使用规范
- 使用session级别的fixture进行登录认证
- 使用function级别的fixture进行数据清理
- 所有fixture必须有清晰的中文docstring

## API封装规范

### API类设计
- 每个业务模块创建独立的API类（如：`ContractAPI`, `ProjectAPI`）
- 所有API方法使用allure.step装饰器
- 统一使用RequestUtil进行HTTP请求
- 方法名使用动词+名词格式（如：`create_contract`, `get_project_list`）

### 请求响应处理
- 统一使用AssertUtil进行响应断言
- 记录详细的请求响应日志
- 敏感信息自动脱敏处理
- 实现自动重试机制

## 配置管理规范

### 配置文件结构
- 主配置：`config/config.yaml`
- 环境配置：`config/config_{env}.yaml`
- 测试数据：`data/{module}_data.yaml`
- 支持环境变量覆盖配置

### 敏感信息处理
- 密码、token等敏感信息使用环境变量
- 配置文件中不得包含真实密码
- 使用示例配置文件（.example后缀）

## 日志记录规范

### 日志级别使用
- INFO：正常业务流程记录
- WARNING：可恢复的异常情况
- ERROR：严重错误和异常
- DEBUG：详细调试信息

### 日志内容要求
- 请求响应详情自动记录
- 敏感信息自动脱敏
- 包含时间戳和执行上下文
- 支持日志文件轮转

## 错误处理规范

### 异常处理
- 网络异常：自动重试机制
- 认证失败：自动重新登录
- 服务器错误：记录详细错误信息
- 参数错误：提供明确的错误提示

### 重试机制
- 默认重试3次
- 指数退避策略
- 记录每次重试的详细信息
- 达到最大重试次数后抛出异常

## 数据管理规范

### 测试数据
- 使用YAML格式存储测试数据
- 按功能模块组织数据文件
- 支持参数化测试
- 包含正向和负向测试数据

### 数据清理
- 测试完成后自动清理创建的数据
- 使用cleanup fixture进行资源管理
- 记录清理操作的执行结果

## 报告生成规范

### Allure报告
- 使用中文标题和描述
- 按功能模块组织测试用例
- 包含详细的测试步骤
- 附加请求响应详情
- 设置合适的严重级别

### 执行命令
```bash
# 运行测试并生成报告
pytest --alluredir=./allure-results

# 启动报告服务
allure serve ./allure-results
```

## 文件组织规范

### 目录结构
```
rpm_auto/
├── common/          # 公共工具模块
├── config/          # 配置文件
├── data/           # 测试数据
├── testcases/      # 测试用例
├── logs/           # 日志文件（自动生成）
├── allure-results/ # 报告数据（自动生成）
└── conftest.py     # pytest配置
```

### 文件命名
- 测试文件：`test_{module}.py`
- API封装：`{module}_api.py`
- 工具类：`{function}_util.py`
- 配置文件：`config.yaml`, `config_{env}.yaml`
- 数据文件：`{module}_data.yaml`

## 版本控制规范

### Git忽略文件
```gitignore
# 日志文件
logs/
*.log

# 测试报告
allure-results/
allure-report/

# 配置文件（包含敏感信息）
config/config.yaml
!config/config.yaml.example

# Python缓存
__pycache__/
*.pyc
*.pyo

# IDE文件
.vscode/
.idea/
*.swp
```

## 安全规范

### 敏感信息保护
- 不在代码中硬编码密码、token
- 使用环境变量存储敏感配置
- 日志输出时自动脱敏
- 配置文件加入.gitignore

### 访问控制
- 测试账号使用专用权限
- 定期更换测试账号密码
- 不在生产环境执行破坏性测试

## 性能优化规范

### 请求优化
- 合理设置超时时间
- 使用连接池复用连接
- 避免不必要的重复请求
- 并发测试时注意资源竞争

### 资源管理
- 及时释放网络连接
- 控制日志文件大小
- 定期清理临时文件
- 监控内存使用情况

## 持续集成规范

### CI/CD配置
- 支持多环境自动化测试
- 集成Allure报告生成
- 测试失败时发送通知
- 定期执行回归测试

### 环境变量配置
```bash
export BASE_URL="https://rpm-uat.pharmaronclinical.com"
export USERNAME="test_user"
export PASSWORD="encrypted_password"
export TEST_ENV="uat"
```

## 代码审查要点

### 必检项目
- [ ] 敏感信息是否正确处理
- [ ] 异常处理是否完善
- [ ] 日志记录是否充分
- [ ] 测试数据是否清理
- [ ] 断言是否充分有效
- [ ] 代码注释是否清晰
- [ ] 命名是否规范
- [ ] 是否遵循项目结构

### 性能检查
- [ ] 是否存在不必要的重复请求
- [ ] 超时设置是否合理
- [ ] 重试机制是否适当
- [ ] 资源是否正确释放

## 常见问题解决

### 登录失败
- 检查验证码配置是否正确
- 确认用户名密码是否有效
- 查看网络连接是否正常

### 测试不稳定
- 增加适当的等待时间
- 检查数据依赖关系
- 确认环境状态是否一致

### 报告生成失败
- 确认Allure工具是否正确安装
- 检查Java环境是否配置
- 验证报告数据目录权限