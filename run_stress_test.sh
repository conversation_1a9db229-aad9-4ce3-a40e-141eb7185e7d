#!/bin/bash

# RPM压测环境自动化测试脚本

echo "🚀 开始RPM压测环境自动化测试..."

# 设置环境变量
export TEST_ENV=stress

# 创建结果目录
mkdir -p ./allure-results-stress
mkdir -p ./allure-report-stress

# 清理旧结果
echo "🧹 清理旧的测试结果..."
rm -rf ./allure-results-stress/*

# 检查配置文件
if [ ! -f "config/config_stress.yaml" ]; then
    echo "❌ 压测环境配置文件不存在: config/config_stress.yaml"
    echo "请复制 config/config.yaml.example 并重命名为 config/config_stress.yaml"
    exit 1
fi

echo "✅ 压测环境配置文件检查通过"

# 运行测试
echo "🧪 执行压测环境测试..."
pytest testcases/ \
    --alluredir=./allure-results-stress \
    --tb=short \
    --maxfail=5 \
    -v \
    --capture=no

# 检查测试结果
TEST_EXIT_CODE=$?
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ 测试执行完成，所有用例通过"
elif [ $TEST_EXIT_CODE -eq 1 ]; then
    echo "⚠️ 测试执行完成，但有用例失败"
else
    echo "❌ 测试执行异常，退出码: $TEST_EXIT_CODE"
fi

# 检查结果文件
RESULT_COUNT=$(find ./allure-results-stress -name "*-result.json" | wc -l)
echo "📊 生成的测试结果文件数量: $RESULT_COUNT"

if [ $RESULT_COUNT -eq 0 ]; then
    echo "⚠️ 警告: 没有生成测试结果文件，钉钉通知可能无法发送详细报告"
fi

# 生成Allure报告
echo "📊 生成Allure测试报告..."
if command -v allure &> /dev/null; then
    allure generate ./allure-results-stress -o ./allure-report-stress --clean
    echo "📈 Allure报告已生成: ./allure-report-stress/index.html"
    
    # 尝试打开报告（可选）
    if command -v open &> /dev/null; then
        echo "🌐 尝试打开报告..."
        open ./allure-report-stress/index.html
    fi
else
    echo "⚠️ Allure命令行工具未安装，跳过报告生成"
    echo "💡 安装方法: npm install -g allure-commandline"
fi

# 手动发送钉钉通知（备用方案）
if [ -f "send_dingtalk_report.py" ]; then
    echo "📤 手动发送钉钉通知..."
    python send_dingtalk_report.py --results-dir ./allure-results-stress
else
    echo "⚠️ 钉钉通知脚本不存在，跳过手动发送"
fi

echo "🎉 压测环境测试完成！"
echo "📁 结果目录: ./allure-results-stress"
echo "📊 报告目录: ./allure-report-stress"

exit $TEST_EXIT_CODE