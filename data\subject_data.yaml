# 受试者管理测试数据
project_config:
  default_project_id: "1947565264656805888"  # 默认项目ID

create_subject:
  # 基础配置
  genders: ["female", "male"]
  
  # 测试场景
  test_scenarios:
    - name: "创建女性受试者"
      gender: "female"
      description: "测试创建女性受试者"
    
    - name: "创建男性受试者"
      gender: "male"
      description: "测试创建男性受试者"

get_subject_list:
  valid_payload:
    pageIndex: 1
    pageSize: 20
    projectId: "1930148914887311362"
    
  search_payload:
    pageIndex: 1
    pageSize: 10
    projectId: "1930148914887311362"
    keyword: "测试"

update_subject:
  test_scenarios:
    - name: "更新性别"
      updates:
        gender: "male"
      description: "测试更新受试者性别"
    
    - name: "更新日期"
      updates:
        filterDate: "2025-08-01"
        knowDate: "2025-08-01"
      description: "测试更新受试者日期信息"
    
    - name: "完整更新"
      updates:
        gender: "female"
        filterDate: "2025-09-01"
        knowDate: "2025-09-01"
      description: "测试完整更新受试者信息"

# 期望的响应数据校验
get_subject_detail_validation:
  expected_response:
    schema:
      code: str
      data: dict
    
    business_rules:
      - field: "code"
        operator: "eq"
        value: "200"
        message: "响应码必须为200"
      
      - field: "data.id"
        operator: "gt"
        value: 0
        message: "受试者ID必须大于0"
      
      - field: "data.randomNo"
        operator: "ne"
        value: ""
        message: "随机编号不能为空"

# 错误码和消息
error_messages:
  unauthorized: "未授权访问"
  not_found: "受试者不存在"
  validation_error: "参数验证失败"
  dependency_error: "依赖数据获取失败"
  delete_failed: "删除受试者失败"
  update_failed: "更新受试者失败"
