"""
访视方案API接口测试用例
"""
import allure
import pytest
from common.apis.visitingplan_api import VisitingPlanAPI
from common.apis.project_api import ProjectAPI
from common.http.assert_util import AssertUtil

@allure.feature("访视方案API接口")
class TestVisitingPlanAPI:
    
    @allure.story("查询项目中心列表")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_project_center_list(self, req, headers, subject_data):
        """测试查询项目中心列表"""
        project_id = subject_data["project_config"]["default_project_id"]
        project_api = ProjectAPI(req)
        
        with allure.step("查询项目中心列表"):
            response = project_api.get_project_center_list(project_id, headers)
        
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)
            
            # 验证响应结构
            response_data = response.json()
            assert "data" in response_data, "响应中缺少data字段"
            assert "records" in response_data["data"], "响应中缺少records字段"
            
            # 验证数据内容
            records = response_data["data"]["records"]
            if records:
                first_record = records[0]
                required_fields = ["id", "deptId", "projectId"]
                for field in required_fields:
                    assert field in first_record, f"中心记录缺少{field}字段"
                    assert first_record[field], f"中心记录{field}字段为空"
    
    @allure.story("查询访视方案列表")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_visit_solution_list(self, req, headers, subject_data):
        """测试查询访视方案列表"""
        project_id = subject_data["project_config"]["default_project_id"]
        visitingplan_api = VisitingPlanAPI(req)
        
        with allure.step("查询访视方案列表"):
            response = visitingplan_api.get_visit_solution_list(project_id, headers)
        
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)
            
            # 验证响应结构
            response_data = response.json()
            assert "data" in response_data, "响应中缺少data字段"
            
            # 验证数据内容
            visit_plans = response_data["data"]
            if visit_plans:
                first_plan = visit_plans[0]
                required_fields = ["id", "name"]
                for field in required_fields:
                    assert field in first_plan, f"访视方案缺少{field}字段"
                    assert first_plan[field], f"访视方案{field}字段为空"
    
    @allure.story("获取访视方案依赖数据")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_visit_plan_dependencies(self, req, headers, subject_data):
        """测试获取访视方案依赖数据"""
        project_id = subject_data["project_config"]["default_project_id"]
        visitingplan_api = VisitingPlanAPI(req)
        
        with allure.step("获取访视方案依赖数据"):
            dependencies = visitingplan_api.get_visit_plan_dependencies(project_id, headers)
        
        with allure.step("验证依赖数据完整性"):
            required_keys = ["visitPlanId"]
            for key in required_keys:
                assert key in dependencies, f"依赖数据缺少{key}"
                assert dependencies[key], f"依赖数据{key}为空"
            
            # 验证额外信息
            assert "visitPlans" in dependencies, "依赖数据缺少visitPlans"
        
        with allure.step("验证依赖数据格式"):
            # 验证数据格式正确性
            visitingplan_api.validate_visit_plan_data(dependencies)
    
    @allure.story("访视方案数据验证")
    @allure.severity(allure.severity_level.NORMAL)
    def test_validate_visit_plan_data_missing_fields(self, req, headers):
        """测试访视方案数据验证 - 缺少必需字段"""
        visitingplan_api = VisitingPlanAPI(req)
        
        # 构造缺少字段的依赖数据
        incomplete_dependencies = {}
        
        with allure.step("验证缺少visitPlanId字段时的异常"):
            with pytest.raises(ValueError) as exc_info:
                visitingplan_api.validate_visit_plan_data(incomplete_dependencies)
            
            assert "visitPlanId" in str(exc_info.value), "异常信息应包含缺少的字段: visitPlanId"