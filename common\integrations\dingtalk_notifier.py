"""
钉钉通知工具 - 用于发送测试报告到钉钉群
"""
import json
import requests
import datetime
import time
import hmac
import hashlib
import base64
import urllib.parse
from typing import Dict, List, Any
from pathlib import Path
import os
from common.core.logger import Logger
from common.core.config_manager import ConfigManager


class DingTalkNotifier:
    """钉钉通知器"""
    
    def __init__(self, config: ConfigManager = None):
        self.logger = Logger().get_logger()
        self.config = config or ConfigManager()
        
        # 使用新的配置获取方法
        dingtalk_config = self.config.get_dingtalk_config()
        self.webhook_url = dingtalk_config["webhook"]
        self.secret = dingtalk_config["secret"]
        self.enabled = dingtalk_config["enabled"]
        self.default_at_all = dingtalk_config["at_all"]
        self.default_at_mobiles = dingtalk_config["at_mobiles"]
        
        # 记录当前环境和钉钉配置状态
        current_env = os.getenv("TEST_ENV", "default")
        base_url = self.config.get("base_url", "未知")
        
        self.logger.info(f"钉钉通知器初始化 - 环境: {current_env}, 基础URL: {base_url}")
        self.logger.info(f"钉钉通知状态: {'启用' if self.enabled else '禁用'}")
        
        if self.enabled and self.webhook_url:
            # 脱敏显示webhook（只显示最后几位）
            masked_webhook = self.webhook_url[-20:] if len(self.webhook_url) > 20 else "***"
            self.logger.info(f"钉钉webhook已配置: ...{masked_webhook}")
        elif self.enabled:
            self.logger.warning("钉钉通知已启用但webhook未配置")
        
    def _generate_sign(self, timestamp: str) -> str:
        """
        生成钉钉加签
        
        Args:
            timestamp: 时间戳字符串
        
        Returns:
            str: 签名字符串
        """
        if not self.secret:
            return ""
        
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign

    def send_markdown_message(self, title: str, content: str, at_all: bool = None, at_mobiles: List[str] = None) -> bool:
        """
        发送Markdown格式的钉钉消息
        
        Args:
            title: 消息标题
            content: 消息内容(Markdown格式)
            at_all: 是否@所有人，None时使用配置默认值
            at_mobiles: 需要@的手机号列表，None时使用配置默认值
            
        Returns:
            bool: 发送是否成功
        """
        # 检查是否启用钉钉通知
        if not self.enabled:
            self.logger.info("钉钉通知已禁用，跳过发送")
            return True
        
        if not self.webhook_url:
            self.logger.warning("钉钉webhook未配置，跳过发送通知")
            return False
        
        # 使用传入参数或配置默认值
        final_at_all = at_all if at_all is not None else self.default_at_all
        final_at_mobiles = at_mobiles if at_mobiles is not None else self.default_at_mobiles
        
        try:
            # 构建请求URL（支持加签）
            url = self.webhook_url
            if self.secret:
                timestamp = str(round(time.time() * 1000))
                sign = self._generate_sign(timestamp)
                url = f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
                self.logger.debug("使用加签模式发送钉钉消息")
            
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": content
                },
                "at": {
                    "atMobiles": final_at_mobiles,
                    "isAtAll": final_at_all
                }
            }
            
            self.logger.info(f"发送钉钉消息 - @所有人: {final_at_all}, @指定人员: {len(final_at_mobiles)}人")
            
            headers = {"Content-Type": "application/json"}
            response = requests.post(url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("errcode") == 0:
                    self.logger.info("钉钉消息发送成功")
                    return True
                else:
                    error_msg = result.get("errmsg", "未知错误")
                    self.logger.error(f"钉钉消息发送失败: errcode={result.get('errcode')}, errmsg={error_msg}")
                    
                    # 特定错误处理
                    if result.get("errcode") == 310000:
                        self.logger.error("钉钉机器人关键词校验失败，请检查消息内容是否包含机器人设置的关键词")
                    elif result.get("errcode") == 300001:
                        self.logger.error("钉钉机器人IP地址不在白名单中")
                    elif result.get("errcode") == 300002:
                        self.logger.error("钉钉机器人加签验证失败，请检查secret配置")
                    
                    return False
            else:
                self.logger.error(f"钉钉消息发送失败，HTTP状态码: {response.status_code}, 响应: {response.text}")
                return False
            
        except requests.exceptions.Timeout:
            self.logger.error("钉钉消息发送超时")
            return False
        except requests.exceptions.ConnectionError:
            self.logger.error("钉钉消息发送连接失败，请检查网络连接")
            return False
        except Exception as e:
            self.logger.error(f"发送钉钉消息异常: {str(e)}")
            return False
    
    def generate_test_report_summary(self, allure_results_path: str = "./allure-results") -> Dict[str, Any]:
        """
        从Allure结果中生成测试报告摘要
        
        Args:
            allure_results_path: Allure结果目录路径
            
        Returns:
            Dict: 包含测试统计信息的字典
        """
        try:
            results_path = Path(allure_results_path)
            if not results_path.exists():
                self.logger.warning(f"Allure结果目录不存在: {allure_results_path}")
                return {}
            
            total_tests = 0
            passed = 0
            failed = 0
            broken = 0
            skipped = 0
            duration = 0
            
            # 遍历所有结果文件
            for result_file in results_path.glob("*-result.json"):
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                        
                    total_tests += 1
                    status = result.get("status", "unknown")
                    
                    if status == "passed":
                        passed += 1
                    elif status == "failed":
                        failed += 1
                    elif status == "broken":
                        broken += 1
                    elif status == "skipped":
                        skipped += 1
                        
                    # 累加测试时长
                    if "stop" in result and "start" in result:
                        duration += (result["stop"] - result["start"])
                        
                except Exception as e:
                    self.logger.warning(f"解析结果文件失败 {result_file}: {str(e)}")
                    continue
            
            # 计算成功率
            success_rate = 0
            if total_tests > 0:
                success_rate = (passed / total_tests) * 100
            
            # 格式化时长
            duration_str = str(datetime.timedelta(seconds=int(duration / 1000)))
            
            return {
                "total_tests": total_tests,
                "passed": passed,
                "failed": failed,
                "broken": broken,
                "skipped": skipped,
                "success_rate": round(success_rate, 2),
                "duration": duration_str,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            self.logger.error(f"生成测试报告摘要失败: {str(e)}")
            return {}
    
    def create_dingtalk_markdown_report(self, summary: Dict[str, Any]) -> str:
        """
        创建钉钉Markdown格式的测试报告
        
        Args:
            summary: 测试报告摘要
            
        Returns:
            str: Markdown格式的报告内容
        """
        if not summary:
            return "### 🚨 测试报告\n\n无法获取测试结果信息，请检查allure-results目录"
        
        # 根据成功率设置表情符号和颜色
        success_rate = summary.get("success_rate", 0)
        total_tests = summary.get("total_tests", 0)
        failed_count = summary.get("failed", 0)
        
        if success_rate >= 95:
            status_icon = "✅"
            status_text = "优秀"
            color_code = "#52c41a"
        elif success_rate >= 80:
            status_icon = "⚠️"
            status_text = "良好"
            color_code = "#faad14"
        else:
            status_icon = "❌"
            status_text = "需要关注"
            color_code = "#f5222d"
        
        # 获取环境信息
        current_env = os.getenv("TEST_ENV", "default")
        base_url = self.config.get('base_url', '未知环境')
        
        if 'stress' in base_url.lower() or current_env == 'stress':
            env_name = "压测环境"
            env_icon = "⚡"
        elif 'uat' in base_url.lower():
            env_name = "UAT环境"
            env_icon = "🧪"
        elif 'prod' in base_url.lower():
            env_name = "生产环境"
            env_icon = "🚀"
        elif 'dev' in base_url.lower():
            env_name = "开发环境"
            env_icon = "🔧"
        else:
            env_name = "测试环境"
            env_icon = "🔬"

        # 压测环境特殊处理
        if current_env == 'stress':
            # 压测环境可能需要显示额外信息
            stress_info = ""
            concurrency = self.config.get('stress_test_concurrency')
            duration = self.config.get('stress_test_duration')
            if concurrency and duration:
                stress_info = f"\n> **压测配置:** 并发数 {concurrency}, 持续时间 {duration//60}分钟"

        markdown_content = f"""## 🎯 RPM自动化测试报告 {status_icon}

> **执行时间:** {summary.get('timestamp', '未知')}  
> **执行环境:** {env_icon} {env_name} ({current_env}){stress_info if current_env == 'stress' else ''}  
> **测试状态:** <font color="{color_code}">**{status_text}**</font>

### 📊 测试结果统计

| 项目 | 数量 | 占比 |
|------|------|------|
| 总测试数 | **{total_tests}** | 100% |
| ✅ 成功 | **{summary.get('passed', 0)}** | {round(summary.get('passed', 0)/total_tests*100, 1) if total_tests > 0 else 0}% |
| ❌ 失败 | **{summary.get('failed', 0)}** | {round(summary.get('failed', 0)/total_tests*100, 1) if total_tests > 0 else 0}% |
| ⚠️ 异常 | **{summary.get('broken', 0)}** | {round(summary.get('broken', 0)/total_tests*100, 1) if total_tests > 0 else 0}% |
| ⏭️ 跳过 | **{summary.get('skipped', 0)}** | {round(summary.get('skipped', 0)/total_tests*100, 1) if total_tests > 0 else 0}% |

### 📈 成功率
<font color="{color_code}" size="5">**{success_rate}%**</font>

### ⏱️ 执行时长
**{summary.get('duration', '未知')}**

---
💡 如需查看详细报告，请访问Allure测试报告系统
"""
        
        return markdown_content
    
    def send_test_report(self, allure_results_path: str = "./allure-results") -> bool:
        """
        发送完整的测试报告到钉钉
        
        Args:
            allure_results_path: Allure结果目录路径
            
        Returns:
            bool: 发送是否成功
        """
        try:
            summary = self.generate_test_report_summary(allure_results_path)
            if not summary:
                self.logger.warning("无法生成测试报告摘要")
                return False
            
            markdown_content = self.create_dingtalk_markdown_report(summary)
            
            # 智能判断是否@所有人（结合环境配置和测试结果）
            success_rate = summary.get("success_rate", 0)
            failed_count = summary.get("failed", 0)
            
            # 基于测试结果的@人逻辑
            should_at_all_by_result = failed_count > 5 or success_rate < 80
            
            # 最终决策：配置的默认值 OR 基于结果的判断
            final_at_all = self.default_at_all or should_at_all_by_result
            
            self.logger.info(f"钉钉通知决策 - 配置默认@所有人: {self.default_at_all}, "
                            f"基于结果@所有人: {should_at_all_by_result}, "
                            f"最终决策: {final_at_all}")
            
            return self.send_markdown_message(
                title=f"RPM自动化测试报告 - {success_rate}%成功率",
                content=markdown_content,
                at_all=final_at_all,
                at_mobiles=self.default_at_mobiles
            )
            
        except Exception as e:
            self.logger.error(f"发送测试报告失败: {str(e)}")
            return False
