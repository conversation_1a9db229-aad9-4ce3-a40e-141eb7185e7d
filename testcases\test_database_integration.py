"""
数据库集成测试示例
演示如何在测试中使用两种数据库访问方式
"""
import pytest
import allure
from dao.user_dao import UserDAO, UserDAOPyMySQL
from dao.project_dao import ProjectDAO, ProjectDAOPyMySQL
from models.user import User
from models.project import Project

@allure.feature("数据库集成测试")
class TestDatabaseIntegration:
    """数据库集成测试类"""
    
    @allure.story("PyMySQL数据库操作")
    class TestPyMySQLOperations:
        """PyMySQL操作测试"""
        
        @allure.step("测试PyMySQL用户CRUD操作")
        def test_pymysql_user_crud(self, db_util_pymysql, test_data_cleaner):
            """测试PyMySQL用户CRUD操作"""
            user_dao = UserDAOPyMySQL()
            
            # 创建用户
            user_id = user_dao.create_user(
                username="test_pymysql_user",
                email="<EMAIL>",
                description="PyMySQL测试用户"
            )
            test_data_cleaner['users'].append(user_id)
            
            assert user_id > 0, "用户创建失败"
            
            # 查询用户
            user = user_dao.find_by_id(user_id)
            assert user is not None, "用户查询失败"
            assert user['username'] == "test_pymysql_user"
            assert user['email'] == "<EMAIL>"
            
            # 根据用户名查询
            user_by_name = user_dao.find_by_username("test_pymysql_user")
            assert user_by_name is not None
            assert user_by_name['id'] == user_id
            
            # 更新用户
            updated_rows = user_dao.update_by_id(user_id, {
                'description': '更新后的描述'
            })
            assert updated_rows == 1, "用户更新失败"
            
            # 验证更新
            updated_user = user_dao.find_by_id(user_id)
            assert updated_user['description'] == '更新后的描述'
        
        @allure.step("测试PyMySQL项目操作")
        def test_pymysql_project_operations(self, db_util_pymysql, test_data_cleaner):
            """测试PyMySQL项目操作"""
            user_dao = UserDAOPyMySQL()
            project_dao = ProjectDAOPyMySQL()
            
            # 先创建用户
            user_id = user_dao.create_user(
                username="project_owner_pymysql",
                email="<EMAIL>"
            )
            test_data_cleaner['users'].append(user_id)
            
            # 创建项目
            project_id = project_dao.create_project(
                name="PyMySQL测试项目",
                code="PYMYSQL_TEST_001",
                create_user_id=user_id,
                description="PyMySQL项目测试"
            )
            test_data_cleaner['projects'].append(project_id)
            
            assert project_id > 0, "项目创建失败"
            
            # 查询项目
            project = project_dao.find_by_id(project_id)
            assert project is not None
            assert project['name'] == "PyMySQL测试项目"
            assert project['code'] == "PYMYSQL_TEST_001"
            
            # 根据用户查询项目
            user_projects = project_dao.find_by_user(user_id)
            assert len(user_projects) >= 1
            assert any(p['id'] == project_id for p in user_projects)
    
    @allure.story("SQLAlchemy数据库操作")
    class TestSQLAlchemyOperations:
        """SQLAlchemy操作测试"""
        
        @allure.step("测试SQLAlchemy用户CRUD操作")
        def test_sqlalchemy_user_crud(self, db_util_sqlalchemy, db_session):
            """测试SQLAlchemy用户CRUD操作"""
            user_dao = UserDAO()
            
            # 创建用户
            user = user_dao.create_user(
                username="test_sqlalchemy_user",
                email="<EMAIL>",
                description="SQLAlchemy测试用户"
            )
            
            assert user.id is not None, "用户创建失败"
            assert user.username == "test_sqlalchemy_user"
            
            # 查询用户
            found_user = user_dao.find_by_id(user.id)
            assert found_user is not None
            assert found_user.username == user.username
            
            # 根据用户名查询
            user_by_name = user_dao.find_by_username("test_sqlalchemy_user")
            assert user_by_name is not None
            assert user_by_name.id == user.id
            
            # 更新用户
            user.description = "更新后的SQLAlchemy描述"
            updated_user = user_dao.update(user)
            assert updated_user.description == "更新后的SQLAlchemy描述"
            
            # 删除用户
            delete_success = user_dao.delete(user)
            assert delete_success, "用户删除失败"
            
            # 验证删除
            deleted_user = user_dao.find_by_id(user.id)
            assert deleted_user is None, "用户删除验证失败"
        
        @allure.step("测试SQLAlchemy项目操作")
        def test_sqlalchemy_project_operations(self, db_util_sqlalchemy, db_session):
            """测试SQLAlchemy项目操作"""
            user_dao = UserDAO()
            project_dao = ProjectDAO()
            
            # 先创建用户
            user = user_dao.create_user(
                username="project_owner_sqlalchemy",
                email="<EMAIL>"
            )
            
            # 创建项目
            project = project_dao.create_project(
                name="SQLAlchemy测试项目",
                code="SQLALCHEMY_TEST_001",
                create_user_id=user.id,
                description="SQLAlchemy项目测试"
            )
            
            assert project.id is not None, "项目创建失败"
            assert project.name == "SQLAlchemy测试项目"
            
            # 查询项目
            found_project = project_dao.find_by_id(project.id)
            assert found_project is not None
            assert found_project.code == "SQLALCHEMY_TEST_001"
            
            # 根据用户查询项目
            user_projects = project_dao.find_by_user(user.id)
            assert len(user_projects) >= 1
            assert any(p.id == project.id for p in user_projects)
            
            # 测试关系查询
            with db_session:
                project_with_user = db_session.query(Project).filter(
                    Project.id == project.id
                ).first()
                assert project_with_user.create_user.username == user.username
        
        @allure.step("测试SQLAlchemy高级查询")
        def test_sqlalchemy_advanced_queries(self, db_util_sqlalchemy, db_session):
            """测试SQLAlchemy高级查询功能"""
            user_dao = UserDAO()
            
            # 创建多个用户
            users = []
            for i in range(3):
                user = user_dao.create_user(
                    username=f"advanced_user_{i}",
                    email=f"advanced_{i}@example.com",
                    status='active' if i % 2 == 0 else 'inactive'
                )
                users.append(user)
            
            # 条件查询
            active_users = user_dao.query(status='active')
            assert len(active_users) >= 2  # 至少有2个激活用户
            
            # 复杂查询
            with db_session:
                # 查询激活用户数量
                active_count = db_session.query(User).filter(
                    User.status == 'active',
                    User.is_active == True
                ).count()
                assert active_count >= 2
                
                # 分页查询
                page_users = db_session.query(User).filter(
                    User.username.like('advanced_user_%')
                ).limit(2).offset(0).all()
                assert len(page_users) <= 2
    
    @allure.story("数据库性能和兼容性")
    class TestDatabaseCompatibility:
        """数据库兼容性测试"""
        
        @allure.step("测试两种方式的数据一致性")
        def test_data_consistency(self, db_util_pymysql, db_util_sqlalchemy):
            """测试PyMySQL和SQLAlchemy的数据一致性"""
            pymysql_dao = UserDAOPyMySQL()
            sqlalchemy_dao = UserDAO()
            
            # 使用PyMySQL创建用户
            pymysql_user_id = pymysql_dao.create_user(
                username="consistency_test_pymysql",
                email="<EMAIL>"
            )
            
            # 使用SQLAlchemy查询
            sqlalchemy_user = sqlalchemy_dao.find_by_username("consistency_test_pymysql")
            assert sqlalchemy_user is not None
            assert sqlalchemy_user.email == "<EMAIL>"
            
            # 使用SQLAlchemy创建用户
            sqlalchemy_user = sqlalchemy_dao.create_user(
                username="consistency_test_sqlalchemy",
                email="<EMAIL>"
            )
            
            # 使用PyMySQL查询
            pymysql_user = pymysql_dao.find_by_username("consistency_test_sqlalchemy")
            assert pymysql_user is not None
            assert pymysql_user['email'] == "<EMAIL>"
        
        @allure.step("测试事务处理")
        def test_transaction_handling(self, db_util_pymysql, db_util_sqlalchemy):
            """测试事务处理功能"""
            # PyMySQL事务测试
            with pytest.raises(Exception):
                with db_util_pymysql.transaction() as trans:
                    connection = trans.connection
                    with connection.cursor() as cursor:
                        cursor.execute(
                            "INSERT INTO sys_user (username, email) VALUES (%s, %s)",
                            ("tx_test_pymysql", "<EMAIL>")
                        )
                        # 故意引发异常
                        raise Exception("测试事务回滚")
            
            # 验证事务回滚
            pymysql_dao = UserDAOPyMySQL()
            user = pymysql_dao.find_by_username("tx_test_pymysql")
            assert user is None, "PyMySQL事务回滚失败"
            
            # SQLAlchemy事务测试
            with pytest.raises(Exception):
                with db_util_sqlalchemy.session_scope() as session:
                    user = User(
                        username="tx_test_sqlalchemy",
                        email="<EMAIL>"
                    )
                    session.add(user)
                    session.flush()
                    # 故意引发异常
                    raise Exception("测试事务回滚")
            
            # 验证事务回滚
            sqlalchemy_dao = UserDAO()
            user = sqlalchemy_dao.find_by_username("tx_test_sqlalchemy")
            assert user is None, "SQLAlchemy事务回滚失败"
        
        @allure.step("测试连接池健康检查")
        def test_connection_health(self, db_util_pymysql, db_util_sqlalchemy):
            """测试连接池健康检查"""
            # PyMySQL健康检查
            pymysql_health = db_util_pymysql.health_check()
            assert pymysql_health, "PyMySQL连接不健康"
            
            # SQLAlchemy健康检查
            sqlalchemy_health = db_util_sqlalchemy.health_check()
            assert sqlalchemy_health, "SQLAlchemy连接不健康"
            
            # 工厂类健康检查
            from common.database.db_factory import DBFactory
            health_status = DBFactory.health_check_all()
            assert all(health_status.values()), f"部分连接不健康: {health_status}"
