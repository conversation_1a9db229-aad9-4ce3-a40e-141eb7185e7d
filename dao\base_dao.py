"""
基础DAO类 - 提供通用的数据库操作方法
"""
from typing import List, Dict, Any, Optional, Type, Union
from abc import ABC, abstractmethod

from common.database.db_factory import DBFactory
from common.database.interfaces.i_db_util import IDBUtil
from common.database.interfaces.i_sqlalchemy_util import ISQLAlchemyUtil
from common.core.logger import Logger
from models.base import BaseModel

logger = Logger().get_logger()

class BaseDAO(ABC):
    """基础DAO抽象类"""
    
    def __init__(self, use_sqlalchemy: bool = True):
        """
        初始化DAO
        
        Args:
            use_sqlalchemy: 是否使用SQLAlchemy，False则使用PyMySQL
        """
        self.use_sqlalchemy = use_sqlalchemy
        self.logger = Logger().get_logger()
        
        if use_sqlalchemy:
            self.db_util: ISQLAlchemyUtil = DBFactory.create_sqlalchemy_util()
        else:
            self.db_util: IDBUtil = DBFactory.create_db_util("pymysql")
    
    @property
    @abstractmethod
    def table_name(self) -> str:
        """表名"""
        pass
    
    @property
    @abstractmethod
    def model_class(self) -> Type[BaseModel]:
        """模型类"""
        pass

class PyMySQLDAO(BaseDAO):
    """基于PyMySQL的DAO基类"""
    
    def __init__(self):
        super().__init__(use_sqlalchemy=False)
    
    def find_by_id(self, id_value: Union[int, str]) -> Optional[Dict[str, Any]]:
        """根据ID查找记录"""
        sql = f"SELECT * FROM {self.table_name} WHERE id = %s AND deleted = 0"
        results = self.db_util.execute_query(sql, (id_value,))
        return results[0] if results else None
    
    def find_all(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """查找所有记录"""
        sql = f"SELECT * FROM {self.table_name} WHERE deleted = 0 LIMIT %s OFFSET %s"
        return self.db_util.execute_query(sql, (limit, offset))
    
    def create(self, data: Dict[str, Any]) -> int:
        """创建记录"""
        fields = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        sql = f"INSERT INTO {self.table_name} ({fields}) VALUES ({placeholders})"
        return self.db_util.execute_insert(sql, tuple(data.values()))
    
    def update_by_id(self, id_value: Union[int, str], data: Dict[str, Any]) -> int:
        """根据ID更新记录"""
        set_clause = ', '.join([f"{k} = %s" for k in data.keys()])
        sql = f"UPDATE {self.table_name} SET {set_clause} WHERE id = %s AND deleted = 0"
        params = list(data.values()) + [id_value]
        return self.db_util.execute_update(sql, params)
    
    def delete_by_id(self, id_value: Union[int, str]) -> int:
        """根据ID软删除记录"""
        sql = f"UPDATE {self.table_name} SET deleted = 1 WHERE id = %s"
        return self.db_util.execute_update(sql, (id_value,))
    
    def count(self, where_clause: str = "", params: tuple = ()) -> int:
        """统计记录数"""
        base_where = "deleted = 0"
        if where_clause:
            where_clause = f"{base_where} AND {where_clause}"
        else:
            where_clause = base_where
            
        sql = f"SELECT COUNT(*) as count FROM {self.table_name} WHERE {where_clause}"
        result = self.db_util.execute_query(sql, params)
        return result[0]['count'] if result else 0

class SQLAlchemyDAO(BaseDAO):
    """基于SQLAlchemy的DAO基类"""
    
    def __init__(self):
        super().__init__(use_sqlalchemy=True)
    
    def find_by_id(self, id_value: Union[int, str]) -> Optional[BaseModel]:
        """根据ID查找记录"""
        return self.db_util.find_by_id(self.model_class, id_value)
    
    def find_all(self, limit: int = 100, offset: int = 0) -> List[BaseModel]:
        """查找所有记录"""
        return self.db_util.find_all(self.model_class, limit, offset)
    
    def create(self, data: Union[Dict[str, Any], BaseModel]) -> BaseModel:
        """创建记录"""
        if isinstance(data, dict):
            obj = self.model_class.from_dict(data)
        else:
            obj = data
        return self.db_util.save(obj)
    
    def update(self, obj: BaseModel) -> BaseModel:
        """更新记录"""
        return self.db_util.save(obj)
    
    def delete(self, obj: BaseModel) -> bool:
        """删除记录"""
        return self.db_util.delete(obj)
    
    def query(self, **filters) -> List[BaseModel]:
        """条件查询"""
        with self.db_util.session_scope() as session:
            query = session.query(self.model_class)
            for key, value in filters.items():
                if hasattr(self.model_class, key):
                    query = query.filter(getattr(self.model_class, key) == value)
            return query.all()
