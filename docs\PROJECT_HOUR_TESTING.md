# 项目工时API测试指南

本文档介绍如何使用项目工时相关的API测试用例。

## 📁 相关文件

- `testcases/test_project_hour.py` - 项目工时API测试用例
- `common/project_hour_api.py` - 项目工时API封装
- `data/project_hour_data.yaml` - 测试数据配置
- `run_project_hour_tests.py` - 测试运行脚本

## 🧪 测试用例概览

### 核心功能测试

1. **用户登录测试** (`test_user_login`)
   - 测试用户登录功能
   - 验证登录响应状态
   - 注意：当前登录接口返回页面而非JSON

2. **项目卡片列表查询** (`test_get_project_card_list`)
   - 测试项目列表卡片视图查询
   - 验证响应数据结构
   - 检查分页信息

3. **项目基本信息查询** (`test_get_project_info`)
   - 测试项目概览基本信息获取
   - 验证项目信息字段
   - 使用默认项目ID进行测试

### 功能扩展测试

4. **分页功能测试** (`test_get_project_card_list_pagination`)
   - 参数化测试不同的分页参数
   - 测试页码: 1, 2
   - 测试页大小: 10, 20, 50

5. **边界值测试** (`test_get_project_card_list_boundary`)
   - 最小页大小 (pageSize=1)
   - 较大页大小 (pageSize=100)
   - 边界页码 (pageIndex=0, 999)

6. **异常场景测试** (`test_get_project_info_invalid_id`)
   - 无效项目ID测试
   - 包括: "0", "999999999", "invalid_id", ""

### 高级测试

7. **完整流程测试** (`test_project_hour_complete_flow`)
   - 组合多个API调用
   - 验证数据一致性
   - 端到端流程验证

8. **并发访问测试** (`test_concurrent_access`)
   - 3个并发线程测试
   - 验证并发安全性
   - 要求80%以上成功率

9. **性能测试** (`test_performance`)
   - 5次连续请求测试
   - 平均响应时间要求 < 5秒
   - 记录详细性能数据

## 🚀 运行测试

### 方法一：使用运行脚本（推荐）

```bash
# 检查环境
python run_project_hour_tests.py check

# 运行所有测试
python run_project_hour_tests.py test

# 运行冒烟测试
python run_project_hour_tests.py test --type smoke

# 运行关键测试
python run_project_hour_tests.py test --type critical

# 运行性能测试
python run_project_hour_tests.py test --type performance

# 生成测试报告
python run_project_hour_tests.py report

# 清理测试结果
python run_project_hour_tests.py clean
```

### 方法二：直接使用pytest

```bash
# 运行所有项目工时测试
pytest testcases/test_project_hour.py --alluredir=./allure-results -v

# 运行特定测试
pytest testcases/test_project_hour.py::TestProjectHour::test_get_project_card_list -v

# 运行关键测试
pytest testcases/test_project_hour.py -m critical --alluredir=./allure-results

# 运行分页测试
pytest testcases/test_project_hour.py::TestProjectHour::test_get_project_card_list_pagination -v
```

## 📊 测试报告

### 生成Allure报告

```bash
# 生成并启动报告服务
allure serve ./allure-results

# 或生成静态报告
allure generate ./allure-results -o ./allure-report --clean
```

### 报告内容

- **测试执行统计**: 通过/失败/跳过数量
- **测试用例详情**: 每个测试的执行步骤
- **性能数据**: 响应时间分析
- **并发测试结果**: 并发安全性验证
- **错误信息**: 失败用例的详细错误

## ⚙️ 配置说明

### 测试数据配置 (`data/project_hour_data.yaml`)

```yaml
# 项目配置
project_config:
  default_project_id: "1930148914887311362"
  test_project_name: "测试项目"

# 分页查询配置
project_card_list:
  valid_queries:
    - pageIndex: 1
      pageSize: 20
      description: "标准分页查询"

# 性能测试配置
performance_test:
  card_list_performance:
    test_count: 5
    max_avg_response_time: 5.0
```

### API配置 (`common/project_hour_api.py`)

当前API包含以下方法：
- `login_user()` - 用户登录
- `get_project_card_list()` - 查询项目卡片列表
- `get_project_info()` - 获取项目基本信息

## 🔧 自定义测试

### 添加新的测试用例

1. 在 `test_project_hour.py` 中添加新的测试方法
2. 使用 `@allure.story()` 和 `@allure.step()` 装饰器
3. 在 `project_hour_data.yaml` 中添加相应的测试数据

示例：
```python
@allure.story("新功能测试")
@allure.severity(allure.severity_level.NORMAL)
def test_new_feature(self, req, headers, project_hour_data):
    """测试新功能"""
    with allure.step("准备测试数据"):
        # 从测试数据中获取配置
        config = project_hour_data["new_feature_config"]
    
    with allure.step("执行API调用"):
        project_api = ProjectAPI(req)
        response = project_api.new_method(config, headers)
    
    with allure.step("验证结果"):
        AssertUtil.assert_response_success(response)
```

### 修改测试参数

在 `project_hour_data.yaml` 中修改相应的配置：
- 调整性能测试的阈值
- 修改并发测试的线程数
- 添加新的边界值测试场景

## 🐛 常见问题

### 1. 登录测试失败
- **原因**: 登录接口返回HTML页面而非JSON
- **解决**: 当前测试只验证状态码，这是正常的

### 2. 项目ID不存在
- **原因**: 默认项目ID在测试环境中不存在
- **解决**: 在 `project_hour_data.yaml` 中更新正确的项目ID

### 3. 性能测试失败
- **原因**: 网络延迟或服务器负载高
- **解决**: 调整 `max_avg_response_time` 阈值

### 4. 并发测试不稳定
- **原因**: 服务器并发处理能力限制
- **解决**: 减少 `thread_count` 或调整 `min_success_rate`

## 📈 测试策略

### 冒烟测试
- 运行核心功能测试
- 快速验证基本功能可用性
- 适合每次部署后的快速验证

### 回归测试
- 运行所有功能测试
- 验证新功能不影响现有功能
- 适合版本发布前的全面验证

### 性能测试
- 专注于响应时间和并发能力
- 监控系统性能指标
- 适合性能优化后的验证

## 🔒 注意事项

1. **环境依赖**: 确保测试环境可访问
2. **数据隔离**: 测试数据不要影响生产环境
3. **并发限制**: 避免过多并发请求影响服务器
4. **敏感信息**: 测试配置中不要包含真实密码
5. **清理资源**: 测试完成后及时清理临时数据

## 📚 扩展阅读

- [主项目README](../README.md) - 完整的框架说明
- [API封装规范](../rpm_auto.md) - API封装开发规范
- [钉钉通知配置](./DINGTALK_SETUP.md) - 测试报告推送配置
