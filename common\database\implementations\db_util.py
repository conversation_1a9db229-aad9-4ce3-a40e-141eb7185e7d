"""
简化的数据库工具类 - 基于PyMySQL的轻量级实现
"""
import time
import pymysql
from pymysql.cursors import DictCursor
from typing import Dict, List, Any, Optional, Union, Tuple
from contextlib import contextmanager
from dbutils.pooled_db import PooledDB

from common.core.config_manager import ConfigManager
from common.core.logger import Logger
from common.core.exceptions import DatabaseError, ConfigurationError
from common.core.security_util import SecurityUtil
from common.database.interfaces.i_db_util import IDBUtil

logger = Logger().get_logger()

class TransactionContext:
    """简化的事务上下文管理器"""
    
    def __init__(self, connection):
        self.connection = connection
        self.logger = Logger().get_logger()
    
    def __enter__(self):
        self.logger.debug("开始数据库事务")
        return self.connection
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            if exc_type is not None:
                self.connection.rollback()
                self.logger.warning(f"事务回滚: {exc_val}")
            else:
                self.connection.commit()
                self.logger.debug("事务提交成功")
        finally:
            self.connection.close()
        return False

class DBUtil(IDBUtil):
    """简化的数据库工具类"""
    
    def __init__(self):
        self.config = ConfigManager().get_database_config()
        self.logger = Logger().get_logger()
        self._connection_pool = None
        self._validate_config()
        self._create_connection_pool()
    
    def _validate_config(self) -> None:
        """验证数据库配置"""
        required_fields = ['host', 'port', 'username', 'password', 'database']
        missing_fields = [field for field in required_fields if not self.config.get(field)]
        
        if missing_fields:
            raise ConfigurationError(
                f"数据库配置缺少必需字段: {', '.join(missing_fields)}",
                config_name="database",
                missing_fields=missing_fields
            )
    
    def _create_connection_pool(self) -> None:
        """创建数据库连接池"""
        try:
            # 获取PyMySQL特定配置
            pymysql_config = self.config.get('pymysql', {})
            pool_size = pymysql_config.get('pool_size', self.config.get('pool_size', 5))
            timeout = self.config.get('timeout', 30)
            charset = pymysql_config.get('charset', self.config.get('charset', 'utf8mb4'))
            
            safe_config = SecurityUtil.sanitize_data(self.config.copy())
            self.logger.info(f"创建PyMySQL连接池: {safe_config['host']}:{safe_config['port']}/{safe_config['database']}")
            
            self._connection_pool = PooledDB(
                creator=pymysql,
                maxconnections=pool_size,
                mincached=1,
                maxcached=min(3, pool_size),
                blocking=True,
                host=self.config['host'],
                port=int(self.config['port']),
                user=self.config['username'],
                password=self.config['password'],
                database=self.config['database'],
                charset=charset,
                cursorclass=DictCursor,
                connect_timeout=timeout,
                autocommit=False
            )
            
            # 测试连接
            with self._get_connection() as connection:
                pass
            
            self.logger.info("PyMySQL连接池创建成功")
            
        except Exception as e:
            error_msg = f"创建PyMySQL连接池失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(
                error_msg,
                host=self.config['host'],
                database=self.config['database'],
                original_error=str(e)
            )
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self._connection_pool.connection()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    def execute_query(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> List[Dict]:
        """执行查询操作"""
        start_time = time.time()
        query_id = SecurityUtil.generate_request_id("QUERY", sql[:30])
        
        try:
            with self._get_connection() as connection:
                with connection.cursor() as cursor:
                    safe_params = SecurityUtil.sanitize_data(params) if params else None
                    self.logger.debug(f"[{query_id}] 执行SQL查询: {sql}")
                    self.logger.debug(f"[{query_id}] 查询参数: {safe_params}")
                    
                    cursor.execute(sql, params or ())
                    results = cursor.fetchall()
                    
                    execution_time = round((time.time() - start_time) * 1000, 2)
                    self.logger.debug(f"[{query_id}] 查询完成, 耗时: {execution_time}ms, 结果数: {len(results)}")
                    
                    return results
                    
        except Exception as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            error_msg = f"数据库查询失败: {str(e)}"
            self.logger.error(f"[{query_id}] {error_msg}, 耗时: {execution_time}ms")
            raise DatabaseError(
                error_msg,
                sql=sql,
                params=SecurityUtil.sanitize_data(params) if params else None,
                execution_time=execution_time,
                original_error=str(e)
            )
    
    def execute_update(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """执行更新操作"""
        start_time = time.time()
        query_id = SecurityUtil.generate_request_id("UPDATE", sql[:30])
        
        try:
            with self._get_connection() as connection:
                with connection.cursor() as cursor:
                    safe_params = SecurityUtil.sanitize_data(params) if params else None
                    self.logger.debug(f"[{query_id}] 执行SQL更新: {sql}")
                    self.logger.debug(f"[{query_id}] 更新参数: {safe_params}")
                    
                    affected_rows = cursor.execute(sql, params or ())
                    connection.commit()
                    
                    execution_time = round((time.time() - start_time) * 1000, 2)
                    self.logger.debug(f"[{query_id}] 更新完成, 耗时: {execution_time}ms, 影响行数: {affected_rows}")
                    
                    return affected_rows
                    
        except Exception as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            error_msg = f"数据库更新失败: {str(e)}"
            self.logger.error(f"[{query_id}] {error_msg}, 耗时: {execution_time}ms")
            raise DatabaseError(
                error_msg,
                sql=sql,
                params=SecurityUtil.sanitize_data(params) if params else None,
                execution_time=execution_time,
                original_error=str(e)
            )
    
    def execute_insert(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """执行插入操作"""
        start_time = time.time()
        query_id = SecurityUtil.generate_request_id("INSERT", sql[:30])
        
        try:
            with self._get_connection() as connection:
                with connection.cursor() as cursor:
                    safe_params = SecurityUtil.sanitize_data(params) if params else None
                    self.logger.debug(f"[{query_id}] 执行SQL插入: {sql}")
                    self.logger.debug(f"[{query_id}] 插入参数: {safe_params}")
                    
                    cursor.execute(sql, params or ())
                    connection.commit()
                    
                    insert_id = cursor.lastrowid
                    
                    execution_time = round((time.time() - start_time) * 1000, 2)
                    self.logger.debug(f"[{query_id}] 插入完成, 耗时: {execution_time}ms, 插入ID: {insert_id}")
                    
                    return insert_id
                    
        except Exception as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            error_msg = f"数据库插入失败: {str(e)}"
            self.logger.error(f"[{query_id}] {error_msg}, 耗时: {execution_time}ms")
            raise DatabaseError(
                error_msg,
                sql=sql,
                params=SecurityUtil.sanitize_data(params) if params else None,
                execution_time=execution_time,
                original_error=str(e)
            )
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        connection = self._connection_pool.connection()
        yield TransactionContext(connection)
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            with self._get_connection() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    return result is not None
        except Exception as e:
            self.logger.error(f"数据库健康检查失败: {str(e)}")
            return False
    
    def close(self):
        """关闭连接池"""
        if self._connection_pool:
            try:
                self._connection_pool.close()
                self.logger.info("PyMySQL连接池已关闭")
            except Exception as e:
                self.logger.error(f"关闭PyMySQL连接池失败: {str(e)}")
