"""
数据库访问接口定义 - 统一的抽象接口
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple
from contextlib import contextmanager

class IDBUtil(ABC):
    """数据库访问统一接口"""
    
    @abstractmethod
    def execute_query(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> List[Dict]:
        """执行查询操作"""
        pass
    
    @abstractmethod
    def execute_update(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """执行更新操作"""
        pass
    
    @abstractmethod
    def execute_insert(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """执行插入操作"""
        pass
    
    @abstractmethod
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        pass
    
    @abstractmethod
    def close(self):
        """关闭连接"""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """健康检查"""
        pass

class ISQLAlchemyUtil(IDBUtil):
    """SQLAlchemy扩展接口"""
    
    @abstractmethod
    def get_session(self):
        """获取ORM会话"""
        pass
    
    @abstractmethod
    def get_engine(self):
        """获取数据库引擎"""
        pass
    
    @abstractmethod
    def create_all(self):
        """创建所有表"""
        pass
    
    @abstractmethod
    def drop_all(self):
        """删除所有表"""
        pass