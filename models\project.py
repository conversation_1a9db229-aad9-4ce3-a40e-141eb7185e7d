"""
项目模型定义
"""
from sqlalchemy import Column, String, Integer, Text, ForeignKey
from sqlalchemy.orm import relationship
from models.base import BaseModel

class Project(BaseModel):
    """项目模型"""
    
    __tablename__ = 'sys_project'
    
    name = Column(String(100), nullable=False, comment="项目名称")
    code = Column(String(50), unique=True, nullable=False, comment="项目编码")
    description = Column(Text, comment="项目描述")
    status = Column(String(20), default='active', comment="项目状态")
    create_user_id = Column(Integer, ForeignKey('sys_user.id'), comment="创建用户ID")
    
    # 关系定义
    create_user = relationship("User", back_populates="projects")
    
    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    def is_active(self) -> bool:
        """检查项目是否激活"""
        return self.status == 'active'