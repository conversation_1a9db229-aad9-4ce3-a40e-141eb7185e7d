"""
访视方案相关API封装
"""
import allure
import time
from common.core.logger import Logger

logger = Logger().get_logger()

class VisitingPlanAPI:
    """访视方案API封装类"""
    
    def __init__(self, request_util):
        self.req = request_util
    
    @allure.step("查询访视方案列表")
    def get_visit_solution_list(self, project_id, headers):
        """查询访视方案列表
        
        Args:
            project_id: 项目ID
            headers: 请求头
            
        Returns:
            response: HTTP响应对象
        """
        url = "/rpm-api/projectVisitSolution/visitSolutionList"
        current_timestamp = int(time.time() * 1000)
        params = {
            "projectId": project_id,
            "_t": current_timestamp
        }
        logger.info(f"查询访视方案列表请求参数: {params}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"查询访视方案列表响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("获取访视方案依赖数据")
    def get_visit_plan_dependencies(self, project_id, headers):
        """获取访视方案相关的依赖数据
        
        Args:
            project_id: 项目ID
            headers: 请求头
            
        Returns:
            dict: 包含访视方案数据的字典
                - visitPlanId: 访视方案ID
                - visitPlans: 完整的访视方案列表
        """
        dependencies = {}
        
        # 获取访视方案信息
        with allure.step("获取访视方案信息"):
            visit_response = self.get_visit_solution_list(project_id, headers)
            if visit_response.status_code == 200:
                visit_data = visit_response.json()
                if visit_data.get("code") == "200" and visit_data.get("data"):
                    visit_plans = visit_data["data"]
                    if visit_plans:
                        # 选择第一个currentVersion大于0的访视方案ID
                        valid_plan = next((plan for plan in visit_plans if plan.get("currentVersion", 0) > 0), None)
                        if valid_plan:
                            dependencies["visitPlanId"] = valid_plan["id"]
                            dependencies["visitPlans"] = visit_plans  # 保存完整列表
                            logger.info(f"获取到访视方案ID: {dependencies['visitPlanId']} (currentVersion: {valid_plan.get('currentVersion')})")
                        else:
                            logger.warning("没有找到currentVersion大于0的访视方案")
                            dependencies["visitPlanId"] = visit_plans[0]["id"]  # 降级使用第一个方案
                            dependencies["visitPlans"] = visit_plans
                            logger.info(f"降级使用第一个访视方案ID: {dependencies['visitPlanId']}")
                    else:
                        logger.warning("访视方案列表为空")
                else:
                    logger.warning(f"获取访视方案失败: {visit_data.get('message', '未知错误')}")
            else:
                logger.error(f"查询访视方案接口失败: {visit_response.status_code}")
        
        return dependencies
    
    @allure.step("验证访视方案数据完整性")
    def validate_visit_plan_data(self, dependencies, required_fields=None):
        """简化的数据验证"""
        if not dependencies.get("visitPlanId"):
            raise ValueError("访视方案ID不能为空")
        return True
