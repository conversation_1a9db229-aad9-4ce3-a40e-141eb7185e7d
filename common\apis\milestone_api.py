"""
里程碑配置API封装
"""

import allure
from common.core.logger import Logger

logger = Logger().get_logger()

class MilestoneApi:
    """里程碑配置API封装类"""
    def __init__(self, request_util):
        self.req = request_util


    @allure.step("项目里程碑目录获取")
    def get_project_milestone(self, headers):
        """项目里程碑目录获取"""
        url = "/rpm-api/projectMilestone/menu"

        
        response = self.req.send_request(method="get", url=url, headers=headers)
        logger.info(f"项目里程碑目录获取响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("项目里程碑-配置列表获取")
    def get_project_milestone_config(self, project_id, headers):
        """项目里程碑-配置列表获取"""
        url = "/rpm-api/projectMilestone/config/list"
        params = {"projectId": project_id}

        logger.info(f"项目里程碑-配置列表获取参数: {params}")
        response = self.req.send_request(method="get", url=url, params=params, headers=headers)
        logger.info(f"项目里程碑-配置列表获取响应: {response.status_code} - {response.text}")
        return response
    @allure.step("中心里程碑目录获取")
    def get_center_milestone(self, headers):
        """中心里程碑目录获取"""
        url = "/rpm-api/projectCenterMilestone/menu"

        
        response = self.req.send_request(method="get", url=url, headers=headers)
        logger.info(f"中心里程碑目录获取响应: {response.status_code} - {response.text}")
        return response
    @allure.step("中心里程碑-配置列表获取")
    def get_center_milestone_config(self, project_id, headers):
        """中心里程碑-配置列表获取"""
        url = "/rpm-api/projectCenterMilestone/config/list"
        params = {"projectId": project_id}

        logger.info(f"中心里程碑-配置列表获取参数: {params}")
        response = self.req.send_request(method="get", url=url, params=params, headers=headers)
        logger.info(f"中心里程碑-配置列表获取响应: {response.status_code} - {response.text}")
        return response
    @allure.step("受试者随访进度保存")
    def save_subject_schedule(self,project_id,headers):
        """受试者随访进度保存"""
        url = "/rpm-api/subjectSchedule/batchEdit"
        json = [{
				"id": "1947565275608133633",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "filter"
			}, {
				"id": "1947565275616522240",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "plan_join_group"
			}, {
				"id": "1947565275612327937",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "in_treatment"
			}, {
				"id": "1947565275616522241",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "safety_follow-up"
			}, {
				"id": "1947565275620716544",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "survival_follow-up"
			}, {
				"id": "1947565275608133632",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "complete_follow-up"
			}, {
				"id": "1947565275612327936",
				"enabled": 0,
				"projectId": project_id,
				"statusDocCode": "filtering_failed"
			}, {
				"id": "1947565275620716545",
				"enabled": 1,
				"projectId": project_id,
				"statusDocCode": "terminate_research"
			}]
        logger.info(f"受试者随访进度保存参数: {json}")
        response = self.req.send_request(method="post", url=url, json=json, headers=headers)
        logger.info(f"受试者随访进度保存响应: {response.status_code} - {response.text}")
        return response
    @allure.step("受试者随访进度列表获取")
    def get_subject_schedule(self, project_id, headers):
        """受试者随访进度列表获取"""
        url = "/rpm-api/subjectSchedule/list"
        params = {"projectId": project_id}

        logger.info(f"受试者随访进度列表获取参数: {params}")
        response = self.req.send_request(method="get", url=url, params=params, headers=headers)
        logger.info(f"受试者随访进度列表获取响应: {response.status_code} - {response.text}")
        return response
        