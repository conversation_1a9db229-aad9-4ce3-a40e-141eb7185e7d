"""
HTTP请求工具类 - 优化版本
"""
import requests
import time
import uuid
import allure
from typing import Optional, Dict, Any, Union
from ..core.logger import Logger
from ..core.exceptions import (
    NetworkError, 
    AuthenticationError, 
    APIRequestError, 
    TimeoutError
)
from ..core.security_util import SecurityUtil
from requests.exceptions import RequestException, Timeout, ConnectionError, HTTPError

logger = Logger().get_logger()

class RequestUtil:
    """HTTP请求工具类 - 支持重试、日志、脱敏和详细异常处理"""
    
    def __init__(self, base_url: str, timeout: int = 30, max_retries: int = 3):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = self._create_session()
        logger.info(f"初始化RequestUtil - base_url: {base_url}, timeout: {timeout}s, max_retries: {max_retries}")

    def _create_session(self) -> requests.Session:
        """创建会话对象"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'RPM-AutoTest/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json;charset=UTF-8'
        })
        return session

    @allure.step("发送HTTP请求")
    def send_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """发送HTTP请求，支持重试和详细异常处理
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: HTTP响应对象
            
        Raises:
            APIRequestError: API请求错误
            NetworkError: 网络连接错误
            CustomTimeoutError: 请求超时错误
            AuthenticationError: 认证错误
        """
        full_url = f"{self.base_url}/{url.lstrip('/')}"
        
        # 生成请求ID用于追踪
        request_id = SecurityUtil.generate_request_id(method, full_url)
        
        # 设置默认超时时间
        kwargs.setdefault('timeout', self.timeout)
        
        # 获取请求头（合并session默认头和传入的头）
        request_headers = self.session.headers.copy()
        if 'headers' in kwargs:
            request_headers.update(kwargs['headers'])
        
        start_time = time.time()
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(f"[{request_id}] 发送请求 - {method} {full_url} (第{attempt + 1}次尝试)")
                
                # 记录请求头（脱敏处理）
                safe_headers = SecurityUtil.sanitize_data(request_headers)
                logger.info(f"[{request_id}] 请求头: {safe_headers}")
                
                # 记录请求参数（脱敏处理）
                safe_kwargs = SecurityUtil.sanitize_data(kwargs.copy())
                logger.info(f"[{request_id}] 请求参数: {safe_kwargs}")
                
                # 单独记录请求体内容（如果存在）
                if 'json' in kwargs:
                    safe_json = SecurityUtil.sanitize_data(kwargs['json'])
                    logger.info(f"[{request_id}] 请求体(JSON): {safe_json}")
                elif 'data' in kwargs:
                    safe_data = SecurityUtil.sanitize_data(kwargs['data'])
                    logger.info(f"[{request_id}] 请求体(DATA): {safe_data}")
                
                # 记录查询参数（如果存在）
                if 'params' in kwargs:
                    safe_params = SecurityUtil.sanitize_data(kwargs['params'])
                    logger.info(f"[{request_id}] 查询参数: {safe_params}")
                
                # 发送请求
                response = self.session.request(method, full_url, **kwargs)
                
                # 计算响应时间
                response_time = round((time.time() - start_time) * 1000, 2)
                
                # 记录响应信息
                logger.info(f"[{request_id}] 响应状态: {response.status_code}, 响应时间: {response_time}ms")
                
                # 记录响应头（脱敏处理）
                safe_response_headers = SecurityUtil.sanitize_data(dict(response.headers))
                logger.debug(f"[{request_id}] 响应头: {safe_response_headers}")
                
                # 记录响应内容（限制长度并脱敏）
                response_text = SecurityUtil.sanitize_data(response.text)
                if len(response_text) > 500:
                    response_text = response_text[:500] + "..."
                logger.info(f"[{request_id}] 响应内容: {response_text}")
                
                # 添加到allure报告
                self._attach_to_allure(request_id, method, full_url, response, response_time, safe_kwargs, safe_headers)
                
                # 检查响应状态
                self._check_response_status(response, method, full_url)
                
                # 如果是正常响应或最后一次尝试，返回结果
                if response.status_code < 500 or attempt == self.max_retries:
                    return response
                    
                # 5xx错误进行重试
                logger.warning(f"[{request_id}] 服务器错误 {response.status_code}，准备重试")
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"[{request_id}] 等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                
            except Timeout as e:
                last_exception = e
                logger.warning(f"[{request_id}] 请求超时 (第{attempt + 1}次尝试): {str(e)}")
                if attempt == self.max_retries:
                    raise TimeoutError(
                        f"请求超时，已重试{self.max_retries}次",
                        timeout_seconds=self.timeout,
                        operation=f"{method} {full_url}"
                    )
                time.sleep(2 ** attempt)
                
            except ConnectionError as e:
                last_exception = e
                logger.warning(f"[{request_id}] 连接错误 (第{attempt + 1}次尝试): {str(e)}")
                if attempt == self.max_retries:
                    raise NetworkError(
                        f"网络连接失败，已重试{self.max_retries}次: {str(e)}",
                        url=full_url,
                        retry_count=self.max_retries
                    )
                time.sleep(2 ** attempt)
                
            except HTTPError as e:
                logger.error(f"[{request_id}] HTTP错误: {str(e)}")
                raise APIRequestError(
                    f"HTTP请求错误: {str(e)}",
                    method=method,
                    url=full_url,
                    status_code=getattr(e.response, 'status_code', None),
                    response_text=getattr(e.response, 'text', None)
                )
                
            except RequestException as e:
                logger.error(f"[{request_id}] 请求异常: {str(e)}")
                raise APIRequestError(
                    f"请求异常: {str(e)}",
                    method=method,
                    url=full_url
                )
                
            except Exception as e:
                logger.error(f"[{request_id}] 未知错误: {str(e)}")
                raise APIRequestError(
                    f"未知请求错误: {str(e)}",
                    method=method,
                    url=full_url
                )
        
        # 如果所有重试都失败了
        if last_exception:
            raise last_exception
    
    def _check_response_status(self, response: requests.Response, method: str, url: str) -> None:
        """检查响应状态，处理特定的错误情况"""
        if response.status_code == 401:
            try:
                error_data = response.json()
                error_message = error_data.get('message', '认证失败')
            except:
                error_message = '认证失败'
            
            raise AuthenticationError(
                f"认证失败: {error_message}",
                response_code=response.status_code
            )
        
        elif response.status_code == 403:
            raise APIRequestError(
                "权限不足，访问被拒绝",
                method=method,
                url=url,
                status_code=response.status_code,
                response_text=response.text
            )
        
        elif response.status_code == 404:
            raise APIRequestError(
                "请求的资源不存在",
                method=method,
                url=url,
                status_code=response.status_code,
                response_text=response.text
            )
    
    def _attach_to_allure(
        self, 
        request_id: str,
        method: str, 
        url: str, 
        response: requests.Response, 
        response_time: float,
        safe_kwargs: Dict[str, Any],
        safe_headers: Dict[str, Any]
    ) -> None:
        """添加请求信息到Allure报告"""
        try:
            # 请求信息
            request_info = (
                f"Request ID: {request_id}\n"
                f"Method: {method}\n"
                f"URL: {url}\n"
                f"Headers: {safe_headers}\n"
                f"Request Data: {safe_kwargs}\n"
                f"Response Time: {response_time}ms\n"
                f"Status Code: {response.status_code}"
            )
            
            allure.attach(
                request_info,
                name="请求信息",
                attachment_type=allure.attachment_type.TEXT
            )
            
            # 响应内容
            response_content = SecurityUtil.sanitize_data(response.text)
            attachment_type = (
                allure.attachment_type.JSON 
                if self._is_json_response(response) 
                else allure.attachment_type.TEXT
            )
            
            allure.attach(
                response_content,
                name="响应内容",
                attachment_type=attachment_type
            )
        except Exception as e:
            logger.warning(f"添加Allure附件失败: {str(e)}")
    
    def _sanitize_kwargs(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """对请求参数进行脱敏处理（保持向后兼容）"""
        return SecurityUtil.sanitize_data(kwargs)
    
    def _is_json_response(self, response: requests.Response) -> bool:
        """判断响应是否为JSON格式"""
        try:
            response.json()
            return True
        except:
            return False
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求的便捷方法"""
        return self.send_request("GET", url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST请求的便捷方法"""
        return self.send_request("POST", url, **kwargs)
    
    def put(self, url: str, **kwargs) -> requests.Response:
        """PUT请求的便捷方法"""
        return self.send_request("PUT", url, **kwargs)
    
    def delete(self, url: str, **kwargs) -> requests.Response:
        """DELETE请求的便捷方法"""
        return self.send_request("DELETE", url, **kwargs)
    
    def close(self) -> None:
        """关闭会话"""
        if hasattr(self, 'session'):
            self.session.close()
            logger.info("HTTP会话已关闭")
