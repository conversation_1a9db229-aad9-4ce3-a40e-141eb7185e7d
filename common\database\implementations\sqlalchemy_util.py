"""
SQLAlchemy数据库工具类 - 提供ORM和Core两种访问方式
"""
import time
from typing import Dict, List, Any, Optional, Union, Tuple, Type
from contextlib import contextmanager
from sqlalchemy import create_engine, text, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.engine import Engine

from common.core.config_manager import ConfigManager
from common.core.logger import Logger
from common.core.exceptions import DatabaseError, ConfigurationError
from common.core.security_util import SecurityUtil
from common.database.interfaces.i_sqlalchemy_util import ISQLAlchemyUtil

logger = Logger().get_logger()

class SQLAlchemyUtil(ISQLAlchemyUtil):
    """SQLAlchemy数据库工具类"""
    
    def __init__(self):
        self.config = ConfigManager().get_database_config()
        self.logger = Logger().get_logger()
        self._engine: Optional[Engine] = None
        self._session_factory = None
        self._metadata = None
        self._validate_config()
        self._create_engine()
        self._create_session_factory()
    
    def _validate_config(self) -> None:
        """验证数据库配置"""
        required_fields = ['host', 'port', 'username', 'password', 'database']
        missing_fields = [field for field in required_fields if not self.config.get(field)]
        
        if missing_fields:
            raise ConfigurationError(
                f"数据库配置缺少必需字段: {', '.join(missing_fields)}",
                config_name="database",
                missing_fields=missing_fields
            )
    
    def _create_engine(self) -> None:
        """创建SQLAlchemy引擎"""
        try:
            # 获取SQLAlchemy特定配置
            sqlalchemy_config = self.config.get('sqlalchemy', {})
            pool_size = sqlalchemy_config.get('pool_size', 10)
            pool_recycle = sqlalchemy_config.get('pool_recycle', 3600)
            echo = sqlalchemy_config.get('echo', False)
            
            # 构建数据库URL
            db_url = (
                f"mysql+pymysql://{self.config['username']}:{self.config['password']}"
                f"@{self.config['host']}:{self.config['port']}/{self.config['database']}"
                f"?charset=utf8mb4"
            )
            
            safe_config = SecurityUtil.sanitize_data(self.config.copy())
            self.logger.info(f"创建SQLAlchemy引擎: {safe_config['host']}:{safe_config['port']}/{safe_config['database']}")
            
            self._engine = create_engine(
                db_url,
                poolclass=QueuePool,
                pool_size=pool_size,
                pool_recycle=pool_recycle,
                pool_pre_ping=True,
                echo=echo,
                connect_args={
                    "connect_timeout": self.config.get('timeout', 30)
                }
            )
            
            # 测试连接
            with self._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.logger.info("SQLAlchemy引擎创建成功")
            
        except Exception as e:
            error_msg = f"创建SQLAlchemy引擎失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(
                error_msg,
                host=self.config['host'],
                database=self.config['database'],
                original_error=str(e)
            )
    
    def _create_session_factory(self) -> None:
        """创建Session工厂"""
        self._session_factory = sessionmaker(bind=self._engine)
        self._metadata = MetaData()
    
    def get_engine(self) -> Engine:
        """获取数据库引擎"""
        return self._engine
    
    def get_session(self) -> Session:
        """获取新的ORM会话"""
        return self._session_factory()
    
    @contextmanager
    def session_scope(self):
        """Session上下文管理器，自动处理提交和回滚"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def execute_query(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> List[Dict]:
        """执行查询操作（Core方式）"""
        start_time = time.time()
        query_id = SecurityUtil.generate_request_id("QUERY", sql[:30])
        
        try:
            with self._engine.connect() as connection:
                safe_params = SecurityUtil.sanitize_data(params) if params else None
                self.logger.debug(f"[{query_id}] 执行SQL查询: {sql}")
                self.logger.debug(f"[{query_id}] 查询参数: {safe_params}")
                
                # 处理不同类型的参数
                if isinstance(params, dict):
                    result = connection.execute(text(sql), params)
                else:
                    result = connection.execute(text(sql), params or ())
                
                # 转换为字典列表
                rows = result.fetchall()
                results = [dict(row._mapping) for row in rows]
                
                execution_time = round((time.time() - start_time) * 1000, 2)
                self.logger.debug(f"[{query_id}] 查询完成, 耗时: {execution_time}ms, 结果数: {len(results)}")
                
                return results
                
        except SQLAlchemyError as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            error_msg = f"SQLAlchemy查询失败: {str(e)}"
            self.logger.error(f"[{query_id}] {error_msg}, 耗时: {execution_time}ms")
            raise DatabaseError(
                error_msg,
                sql=sql,
                params=SecurityUtil.sanitize_data(params) if params else None,
                execution_time=execution_time,
                original_error=str(e)
            )
    
    def execute_update(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """执行更新操作（Core方式）"""
        start_time = time.time()
        query_id = SecurityUtil.generate_request_id("UPDATE", sql[:30])
        
        try:
            with self._engine.connect() as connection:
                with connection.begin():
                    safe_params = SecurityUtil.sanitize_data(params) if params else None
                    self.logger.debug(f"[{query_id}] 执行SQL更新: {sql}")
                    self.logger.debug(f"[{query_id}] 更新参数: {safe_params}")
                    
                    if isinstance(params, dict):
                        result = connection.execute(text(sql), params)
                    else:
                        result = connection.execute(text(sql), params or ())
                    
                    affected_rows = result.rowcount
                    
                    execution_time = round((time.time() - start_time) * 1000, 2)
                    self.logger.debug(f"[{query_id}] 更新完成, 耗时: {execution_time}ms, 影响行数: {affected_rows}")
                    
                    return affected_rows
                    
        except SQLAlchemyError as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            error_msg = f"SQLAlchemy更新失败: {str(e)}"
            self.logger.error(f"[{query_id}] {error_msg}, 耗时: {execution_time}ms")
            raise DatabaseError(
                error_msg,
                sql=sql,
                params=SecurityUtil.sanitize_data(params) if params else None,
                execution_time=execution_time,
                original_error=str(e)
            )
    
    def execute_insert(self, sql: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """执行插入操作（Core方式）"""
        start_time = time.time()
        query_id = SecurityUtil.generate_request_id("INSERT", sql[:30])
        
        try:
            with self._engine.connect() as connection:
                with connection.begin():
                    safe_params = SecurityUtil.sanitize_data(params) if params else None
                    self.logger.debug(f"[{query_id}] 执行SQL插入: {sql}")
                    self.logger.debug(f"[{query_id}] 插入参数: {safe_params}")
                    
                    if isinstance(params, dict):
                        result = connection.execute(text(sql), params)
                    else:
                        result = connection.execute(text(sql), params or ())
                    
                    insert_id = result.lastrowid or 0
                    
                    execution_time = round((time.time() - start_time) * 1000, 2)
                    self.logger.debug(f"[{query_id}] 插入完成, 耗时: {execution_time}ms, 插入ID: {insert_id}")
                    
                    return insert_id
                    
        except SQLAlchemyError as e:
            execution_time = round((time.time() - start_time) * 1000, 2)
            error_msg = f"SQLAlchemy插入失败: {str(e)}"
            self.logger.error(f"[{query_id}] {error_msg}, 耗时: {execution_time}ms")
            raise DatabaseError(
                error_msg,
                sql=sql,
                params=SecurityUtil.sanitize_data(params) if params else None,
                execution_time=execution_time,
                original_error=str(e)
            )
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器（Core方式）"""
        connection = self._engine.connect()
        trans = connection.begin()
        try:
            self.logger.debug("开始SQLAlchemy事务")
            yield connection
            trans.commit()
            self.logger.debug("SQLAlchemy事务提交成功")
        except Exception as e:
            trans.rollback()
            self.logger.warning(f"SQLAlchemy事务回滚: {e}")
            raise
        finally:
            connection.close()
    
    def create_all(self):
        """创建所有表"""
        try:
            # 导入所有模型以确保它们被注册
            from models import Base
            Base.metadata.create_all(self._engine)
            self.logger.info("所有数据库表创建成功")
        except Exception as e:
            error_msg = f"创建数据库表失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg, original_error=str(e))
    
    def drop_all(self):
        """删除所有表"""
        try:
            from models import Base
            Base.metadata.drop_all(self._engine)
            self.logger.info("所有数据库表删除成功")
        except Exception as e:
            error_msg = f"删除数据库表失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg, original_error=str(e))
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            with self._engine.connect() as connection:
                connection.execute(text("SELECT 1"))
                return True
        except Exception as e:
            self.logger.error(f"SQLAlchemy健康检查失败: {str(e)}")
            return False
    
    def close(self):
        """关闭引擎"""
        if self._engine:
            try:
                self._engine.dispose()
                self.logger.info("SQLAlchemy引擎已关闭")
            except Exception as e:
                self.logger.error(f"关闭SQLAlchemy引擎失败: {str(e)}")
    
    # ORM辅助方法
    def save(self, obj) -> Any:
        """保存对象到数据库"""
        with self.session_scope() as session:
            session.add(obj)
            session.flush()  # 获取ID但不提交
            session.refresh(obj)  # 刷新对象状态
            return obj
    
    def delete(self, obj) -> bool:
        """从数据库删除对象"""
        try:
            with self.session_scope() as session:
                session.delete(obj)
                return True
        except Exception as e:
            self.logger.error(f"删除对象失败: {str(e)}")
            return False
    
    def find_by_id(self, model_class: Type, id_value: Any) -> Optional[Any]:
        """根据ID查找对象"""
        with self.session_scope() as session:
            return session.get(model_class, id_value)
    
    def find_all(self, model_class: Type, limit: int = 100, offset: int = 0) -> List[Any]:
        """查找所有对象"""
        with self.session_scope() as session:
            return session.query(model_class).offset(offset).limit(limit).all()
