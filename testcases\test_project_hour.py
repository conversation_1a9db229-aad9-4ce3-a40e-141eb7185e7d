"""
项目管理-项目任务及工时API接口测试用例
"""
import requests
import allure
import pytest
import logging
import os
from pathlib import Path
from common.apis.project_hour_api import ProjectAPI
from common.http.assert_util import AssertUtil


# 确保logs目录存在
Path("logs").mkdir(exist_ok=True)

# 初始化标准日志记录器，避免递归问题
logger = logging.getLogger(__name__)
if not logger.handlers:
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    file_handler = logging.FileHandler('logs/test.log', encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.setLevel(logging.INFO)

test_env="stress"

@allure.feature("项目管理-项目任务及工时")
class TestProjectHour:
    @allure.story("用户登录")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_user_login(self, req, config):
        """测试用户登录功能"""
        with allure.step("准备登录数据"):
            auth_config = config.get_auth_config()
            payload = {
                "username": auth_config["username"],
                "password": auth_config["password"],
                "captcha": auth_config["captcha"],
            }
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json;charset=UTF-8",
                "appid": "app-7zpfoeqntj6"
            }

        with allure.step("执行API用户登录"):
            project_api = ProjectAPI(req)
            response = project_api.login_user(payload, headers)

        with allure.step("验证登录响应"):
            AssertUtil.assert_status_code_200(response)
            # API登录接口返回JSON格式
            try:
                response_json = response.json()
                assert "code" in response_json, "响应中应包含code字段"
                # 记录登录结果
                allure.attach(
                    f"登录响应: {response_json}",
                    "登录响应详情",
                    allure.attachment_type.TEXT
                )
            except Exception as e:
                logger.warning(f"解析登录响应JSON失败: {str(e)}")
                # 如果不是JSON响应，验证响应内容不为空
                assert response.text is not None, "登录响应内容不能为空"

            logger.info("用户登录测试完成")

    
    @allure.story("查询项目管理工时详情")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_get_contract_hour(self, req, headers, project_hour_data):
        """测试查询合同工时详情"""
        with allure.step("准备查询参数"):
            contract_id = project_hour_data["project_config"]["contract_id"]
        # headers已经包含了Authorization和Appid
        # 直接使用即可
        with allure.step("调用获取合同工时API"):
            project_api = ProjectAPI(req)
            response = project_api.get_contract_hour(contract_id, headers)

        with allure.step("验证查询响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            # 验证响应数据结构
            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"

            # 验证分页信息
            data = response_json["data"]
            if isinstance(data, dict):
                # 如果data是字典，可能包含分页信息
                if "total" in data:
                    AssertUtil.assert_field_type(response, "data.total", int)
                    assert data["total"] >= 0, "总数应该大于等于0"

                if "list" in data:
                    assert isinstance(data["list"], list), "项目列表应该是数组"

            logger.info(f"项目卡片视图查询成功，返回数据: {len(str(response.text))} 字符")


    @allure.story("合同管理--查询报价单元字段数量")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_get_contract_quotation(self, req, headers, project_hour_data):
        """合同管理--查询报价单元字段数量"""
        with allure.step("准备项目ID"):
            project_id = project_hour_data["project_config"]["default_project_id"]
            allure.attach(project_id, "项目ID", allure.attachment_type.TEXT)

        with allure.step("执行报价单元查询"):
            project_api = ProjectAPI(req)
            response = project_api.get_contract_quotation(project_id, headers)

        with allure.step("验证报价单元响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            # 验证响应数据结构
            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"
            # 预期的字段数量
            expected_field_count = 2

            # 获取实际返回的字段数量
            actual_field_count = len(response.keys())
            # 判断字段数量是否符合预期
            if actual_field_count == expected_field_count or actual_field_count > expected_field_count:
                logger.info(f"字段数量检查通过: 存在{actual_field_count}个报价单元")
            else:
                logger.info(f"字段数量检查失败: 项目管理_WBS配置无数据,请更换项目")
            return response

            # 验证项目基本信息字段
            data = response_json["data"]
            if isinstance(data, dict):
                # 验证可能存在的项目信息字段
                expected_fields = ["projectId", "projectName", "status"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None, f"项目信息字段{field}不应为空"

            logger.info("项目基本信息查询成功")


    @allure.story("新增研究中心配置")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_create_center(self, req, headers):
        """新增研究中心配置"""
        with allure.step("准备发起请求"):
            payload = {
            "projectId": "1945711786514358274",
            "centerId": "1901903920767315968",
            "centerNo": "0909",
            "centerStandardCode": "13-HLJ-HLJJK",
            "deptId": "1901904071959392258",
            "researcherIds": [
                "1901904179056750595"
            ],
            "isLeader": 1,
            "contractGroupPlanNumber": 5,
            "groupPlanNumber": 5,
            "centerStartTime": "2025-09-30",
            "centerType": 1
            }

        with allure.step("新增结果查询"):
            project_api = ProjectAPI(req)
            response = project_api.create_center(payload, headers)

        with allure.step("验证新增响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)


    @allure.story("新增非访视计划")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_create_NonVisitTaskInfo(self, req, headers):
        """新增非访视计划"""
        with allure.step("准备发起请求"):
                payload = [
        {
            "projectWbsContentId": "1957342616347906050",
            "planWorkHours": 3,
            "finish": 0,
            "priorityCode": "height",
            "planTime": "2025-09-11",
            "projectId": "1945711786514358274",
            "taskType": "nonvisit",
            "wbsContentName": "CR010209启动支持相关其他事项，请在备注说明",
            "wbsContentCode": "CR010209",
            "workClassifyCode": "CR0102",
            "wbsQuoteProjectHours": 10,
            "workContentRemark": "",
            "projectCenterId": "1957343640764715010",
            "centerName": "昆明市第三人民医院 (外科)"
        }
        ]

        with allure.step("新增结果查询"):
            project_api = ProjectAPI(req)
            response = project_api.create_NonVisitTaskInfo(payload, headers)

        with allure.step("验证新增响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)


    @allure.story("切换角色CRC")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_Switch_Role(self, req, headers):
        """切换角色CRC"""
        with allure.step("准备发起请求"):
            payload = {
            "roleId": "1806235754282790916"
            }

        with allure.step("切换结果查询"):
            project_api = ProjectAPI(req)
            response = project_api.create_NonVisitTaskInfo(payload, headers)

        with allure.step("验证切换结果"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)
 

    @allure.story("完成非访视计划")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_Finish_NonVisitTaskInfo(self, req, headers):
        """完成非访视计划"""
        with allure.step("准备发起请求"):
            payload = {
                "actualCompletionDate": "2025-09-10",
                "actualWorkHours": 3,
                "isComplete": 1,
                "remarks": "完成非访视计划",
                "status": "finished",
                "taskId": "1962791232809017345",
                "taskName": "CR010209启动支持相关其他事项，请在备注说明",
                "taskType": "nonvisit"
                }      

        with allure.step("完成结果查询"):
            project_api = ProjectAPI(req)
            response = project_api.Finish_NonVisitTaskInfo(payload, headers)

        with allure.step("验证完成响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)


    @allure.story("访问项目概览-项目工时指标")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_get_project_hour(self, req, headers):
        """访问项目概览-项目工时指标"""
        with allure.step("准备查询参数"):
            payload = {
                "pageIndex": 1,
                "pageSize": 20
            }

        with allure.step("执行项目工时查询"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_hour(payload, headers)

        with allure.step("验证查询响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            # 验证响应数据结构
            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"

            # 验证分页信息
            data = response_json["data"]
            if isinstance(data, dict):
                # 如果data是字典，可能包含分页信息
                if "total" in data:
                    AssertUtil.assert_field_type(response, "data.total", int)
                    assert data["total"] >= 0, "总数应该大于等于0"

                if "list" in data:
                    assert isinstance(data["list"], list), "项目列表应该是数组"

            logger.info(f"项目工时详情查询成功，返回数据: {len(str(response.text))} 字符")


    @allure.story("访问项目概览-月度项目工时指标")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_get_project_monhour(self, req, headers):
        """访问项目概览-月度项目工时指标"""
        with allure.step("准备查询参数"):
            payload = {
                "pageIndex": 1,
                "pageSize": 20
            }

        with allure.step("执行月度工时查询"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_monhour(payload, headers)

        with allure.step("验证查询响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            # 验证响应数据结构
            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"

            # 验证分页信息
            data = response_json["data"]
            if isinstance(data, dict):
                # 如果data是字典，可能包含分页信息
                if "total" in data:
                    AssertUtil.assert_field_type(response, "data.total", int)
                    assert data["total"] >= 0, "总数应该大于等于0"

                if "list" in data:
                    assert isinstance(data["list"], list), "项目列表应该是数组"

            logger.info(f"月度工时详情查询成功，返回数据: {len(str(response.text))} 字符")



    @allure.story("项目信息查询 - 异常场景")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_project_info_invalid_id(self, req, headers):
        """测试使用无效项目ID查询项目信息"""
        invalid_project_ids = [
            "0",           # 无效ID
            "999999999",   # 不存在的ID
            "invalid_id",  # 非数字ID
            "",            # 空ID
        ]

        project_api = ProjectAPI(req)

        for invalid_id in invalid_project_ids:
            with allure.step(f"测试无效项目ID: {invalid_id}"):
                try:
                    response = project_api.get_project_info(invalid_id, headers)

                    # 记录响应结果
                    allure.attach(
                        f"项目ID: {invalid_id}, 状态码: {response.status_code}, 响应: {response.text[:200]}",
                        f"无效ID测试-{invalid_id}",
                        allure.attachment_type.TEXT
                    )

                    # 对于无效ID，接口可能返回错误或空数据，这都是正常的
                    # 主要确保接口不会崩溃
                    assert response.status_code in [200, 400, 404], f"无效ID {invalid_id} 返回了意外的状态码: {response.status_code}"

                except Exception as e:
                    logger.warning(f"无效项目ID {invalid_id} 测试异常: {str(e)}")
                    # 记录异常但不失败测试，因为这可能是预期的行为
                    allure.attach(
                        f"项目ID: {invalid_id}, 异常: {str(e)}",
                        f"无效ID异常-{invalid_id}",
                        allure.attachment_type.TEXT
                    )

    @allure.story("项目工时管理完整流程")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_project_hour_complete_flow(self, req, headers, project_hour_data):
        """测试项目工时管理的完整流程"""
        project_api = ProjectAPI(req)
        project_id = project_hour_data["project_config"]["default_project_id"]

        # 1. 查询项目管理工时详情
        with allure.step("1. 查询项目管理工时详情"):
            card_payload = {"pageIndex": 1, "pageSize": 20}
            card_response = project_api.get_contract_hour(card_payload, headers)
            AssertUtil.assert_response_success(card_response)

        # 2. 合同管理--查询报价单元字段数量
        with allure.step("2. 合同管理--查询报价单元字段数量"):
            info_response = project_api.get_contract_quotation(project_id, headers)
            AssertUtil.assert_response_success(info_response)

        # 3. 新增研究中心配置
        with allure.step("3. 新增研究中心配置"):
            info_response = project_api.create_center(project_id, headers)
            AssertUtil.assert_response_success(info_response)


        # 4. 新增非访视计划
        with allure.step("4. 新增非访视计划"):
            info_response = project_api.create_NonVisitTaskInfo(project_id, headers)
            AssertUtil.assert_response_success(info_response)


        # 5. 切换角色CRC
        with allure.step("5. 切换角色CRC"):
            info_response = project_api.Switch_Role(project_id, headers)
            AssertUtil.assert_response_success(info_response)

        # 6. 完成非访视计划
        with allure.step("6. 完成非访视计划"):
            info_response = project_api.Finish_NonVisitTaskInfo(project_id, headers)
            AssertUtil.assert_response_success(info_response)

        # 7. 验证数据一致性
        with allure.step("3. 验证数据一致性"):
            card_data = card_response.json()["data"]
            info_data = info_response.json()["data"]

            # 验证两个接口都返回了有效数据
            assert card_data is not None, "卡片列表数据不能为空"
            assert info_data is not None, "项目信息数据不能为空"

            # 记录完整流程的执行结果
            allure.attach(
                f"卡片列表查询成功，项目信息查询成功\n卡片数据类型: {type(card_data)}\n项目信息数据类型: {type(info_data)}",
                "完整流程执行结果",
                allure.attachment_type.TEXT
            )

            logger.info("项目工时管理完整流程测试成功")




    # def test_api_response_has_value():
    #     # 发送API请求
    #     response = requests.get('https://api.example.com/endpoint')
    #     response_data = response.json()
        
    #     # 断言特定字段存在且有值
    #     assert 'target_field' in response_data, "响应中缺少target_field字段"
    #     assert response_data['target_field'] is not None, "target_field字段值为null"
        
    #     # 根据字段类型进行更精确的断言
    #     if isinstance(response_data['target_field'], str):
    #         assert response_data['target_field'].strip() != "", "target_field字段为空字符串"
    #     elif isinstance(response_data['target_field'], list):
    #         assert len(response_data['target_field']) > 0, "target_field字段为空数组"
    #     elif isinstance(response_data['target_field'], dict):
    #         assert response_data['target_field'], "target_field字段为空对象"
    #     # 其他类型检查...