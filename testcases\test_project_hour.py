"""
项目管理-项目任务及工时API接口测试用例
"""
import requests
import allure
import pytest
from common.http.assert_util import AssertUtil
from common.core.logger import Logger
from common.core.config_manager import ConfigManager
from common.apis.project_hour_api import ProjectAPI
from common.apis.subject_api import SubjectAPI
from common.core.exceptions import APIRequestError, ValidationError
import conftest

# 初始化日志记录器
logger = Logger().get_logger()

test_env="stress"
@allure.feature("项目管理-项目任务及工时")
class TestProjectHour:

    @allure.token(req: RequestUtil, config: ConfigManager, captcha_and_checkkey: str)
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_user_login(self, req, config):
        """测试用户登录功能"""
        with allure.step("准备登录数据"):
            auth_config = config.get_auth_config()
            payload = {
                "username": auth_config["username"],
                "password": auth_config["password"],
                "checkKey": auth_config.get("checkKey", ""),
                "captcha": auth_config["captcha"],
            }
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json;charset=UTF-8",
                "appid": "app-7zpfoeqntj6"
            }

        with allure.step("执行API用户登录"):
            project_api = ProjectAPI(req)
            response = project_api.login_user(payload, headers)

        with allure.step("验证登录响应"):
            AssertUtil.assert_status_code_200(response)
            # API登录接口返回JSON格式
            try:
                response_json = response.json()
                assert "code" in response_json, "响应中应包含code字段"
                # 记录登录结果
                allure.attach(
                    f"登录响应: {response_json}",
                    "登录响应详情",
                    allure.attachment_type.TEXT
                )
            except Exception as e:
                logger.warning(f"解析登录响应JSON失败: {str(e)}")
                # 如果不是JSON响应，验证响应内容不为空
                assert response.text is not None, "登录响应内容不能为空"

            logger.info("用户登录测试完成")

    @allure.story("查询项目管理卡片视图")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_get_project_card_list(self, req, headers):
        """测试查询项目列表卡片视图"""
        with allure.step("准备查询参数"):
            payload = {
                "pageIndex": 1,
                "pageSize": 20
            }

        with allure.step("执行卡片视图查询"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_card_list(payload, headers)

        with allure.step("验证查询响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            # 验证响应数据结构
            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"

            # 验证分页信息
            data = response_json["data"]
            if isinstance(data, dict):
                # 如果data是字典，可能包含分页信息
                if "total" in data:
                    AssertUtil.assert_field_type(response, "data.total", int)
                    assert data["total"] >= 0, "总数应该大于等于0"

                if "list" in data:
                    assert isinstance(data["list"], list), "项目列表应该是数组"

            logger.info(f"项目卡片视图查询成功，返回数据: {len(str(response.text))} 字符")

    @allure.story("查询项目管理卡片视图 - 分页测试")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.parametrize("page_index,page_size", [
        (1, 10),
        (1, 20),
        (2, 10),
        (1, 50)
    ])
    def test_get_project_card_list_pagination(self, req, headers, page_index, page_size):
        """测试项目卡片视图分页功能"""
        with allure.step(f"准备分页参数: 第{page_index}页，每页{page_size}条"):
            payload = {
                "pageIndex": page_index,
                "pageSize": page_size
            }

        with allure.step("执行分页查询"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_card_list(payload, headers)

        with allure.step("验证分页响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"

            # 验证分页参数生效
            allure.attach(
                f"页码: {page_index}, 页大小: {page_size}",
                "分页参数",
                allure.attachment_type.TEXT
            )

            logger.info(f"分页测试完成: 第{page_index}页，每页{page_size}条")

    @allure.story("查询项目管理卡片视图 - 边界值测试")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_project_card_list_boundary(self, req, headers):
        """测试项目卡片视图边界值"""
        test_cases = [
            {"pageIndex": 1, "pageSize": 1, "description": "最小页大小"},
            {"pageIndex": 1, "pageSize": 100, "description": "较大页大小"},
            {"pageIndex": 0, "pageSize": 20, "description": "页码为0"},
            {"pageIndex": 999, "pageSize": 20, "description": "很大的页码"}
        ]

        project_api = ProjectAPI(req)

        for test_case in test_cases:
            with allure.step(f"测试{test_case['description']}"):
                payload = {
                    "pageIndex": test_case["pageIndex"],
                    "pageSize": test_case["pageSize"]
                }

                response = project_api.get_project_card_list(payload, headers)

                # 对于边界值，我们主要验证接口不会报错
                AssertUtil.assert_status_code_200(response)

                # 记录测试结果
                allure.attach(
                    f"参数: {payload}, 状态码: {response.status_code}",
                    f"边界值测试-{test_case['description']}",
                    allure.attachment_type.TEXT
                )

                logger.info(f"边界值测试完成: {test_case['description']}")

    @allure.story("访问项目概览-基本信息")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_get_project_info(self, req, headers, subject_data):
        """测试访问项目概览基本信息"""
        with allure.step("准备项目ID"):
            project_id = subject_data["project_config"]["default_project_id"]
            allure.attach(project_id, "项目ID", allure.attachment_type.TEXT)

        with allure.step("执行项目信息查询"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_info(project_id, headers)

        with allure.step("验证项目信息响应"):
            AssertUtil.assert_status_code_200(response)
            AssertUtil.assert_response_success(response)

            # 验证响应数据结构
            response_json = response.json()
            assert "data" in response_json, "响应中应包含data字段"

            # 验证项目基本信息字段
            data = response_json["data"]
            if isinstance(data, dict):
                # 验证可能存在的项目信息字段
                expected_fields = ["projectId", "projectName", "status"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None, f"项目信息字段{field}不应为空"

            logger.info("项目基本信息查询成功")

    @allure.story("项目信息查询 - 异常场景")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_project_info_invalid_id(self, req, headers):
        """测试使用无效项目ID查询项目信息"""
        invalid_project_ids = [
            "0",           # 无效ID
            "999999999",   # 不存在的ID
            "invalid_id",  # 非数字ID
            "",            # 空ID
        ]

        project_api = ProjectAPI(req)

        for invalid_id in invalid_project_ids:
            with allure.step(f"测试无效项目ID: {invalid_id}"):
                try:
                    response = project_api.get_project_info(invalid_id, headers)

                    # 记录响应结果
                    allure.attach(
                        f"项目ID: {invalid_id}, 状态码: {response.status_code}, 响应: {response.text[:200]}",
                        f"无效ID测试-{invalid_id}",
                        allure.attachment_type.TEXT
                    )

                    # 对于无效ID，接口可能返回错误或空数据，这都是正常的
                    # 主要确保接口不会崩溃
                    assert response.status_code in [200, 400, 404], f"无效ID {invalid_id} 返回了意外的状态码: {response.status_code}"

                except Exception as e:
                    logger.warning(f"无效项目ID {invalid_id} 测试异常: {str(e)}")
                    # 记录异常但不失败测试，因为这可能是预期的行为
                    allure.attach(
                        f"项目ID: {invalid_id}, 异常: {str(e)}",
                        f"无效ID异常-{invalid_id}",
                        allure.attachment_type.TEXT
                    )

    @allure.story("项目工时管理完整流程")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_project_hour_complete_flow(self, req, headers, subject_data):
        """测试项目工时管理的完整流程"""
        project_api = ProjectAPI(req)
        project_id = subject_data["project_config"]["default_project_id"]

        # 1. 查询项目卡片列表
        with allure.step("1. 查询项目卡片列表"):
            card_payload = {"pageIndex": 1, "pageSize": 20}
            card_response = project_api.get_project_card_list(card_payload, headers)
            AssertUtil.assert_response_success(card_response)

        # 2. 获取项目基本信息
        with allure.step("2. 获取项目基本信息"):
            info_response = project_api.get_project_info(project_id, headers)
            AssertUtil.assert_response_success(info_response)

        # 3. 验证数据一致性
        with allure.step("3. 验证数据一致性"):
            card_data = card_response.json()["data"]
            info_data = info_response.json()["data"]

            # 验证两个接口都返回了有效数据
            assert card_data is not None, "卡片列表数据不能为空"
            assert info_data is not None, "项目信息数据不能为空"

            # 记录完整流程的执行结果
            allure.attach(
                f"卡片列表查询成功，项目信息查询成功\n卡片数据类型: {type(card_data)}\n项目信息数据类型: {type(info_data)}",
                "完整流程执行结果",
                allure.attachment_type.TEXT
            )

            logger.info("项目工时管理完整流程测试成功")

    @allure.story("并发访问测试")
    @allure.severity(allure.severity_level.NORMAL)
    def test_concurrent_access(self, req, headers):
        """测试并发访问项目接口"""
        import threading

        project_api = ProjectAPI(req)
        results = []

        def concurrent_request():
            """并发请求函数"""
            try:
                payload = {"pageIndex": 1, "pageSize": 10}
                response = project_api.get_project_card_list(payload, headers)
                results.append({
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "thread_id": threading.current_thread().ident
                })
            except Exception as e:
                results.append({
                    "error": str(e),
                    "success": False,
                    "thread_id": threading.current_thread().ident
                })

        with allure.step("执行并发请求"):
            threads = []
            thread_count = 3  # 创建3个并发线程

            for _ in range(thread_count):
                thread = threading.Thread(target=concurrent_request)
                threads.append(thread)
                thread.start()

            # 等待所有线程完成
            for thread in threads:
                thread.join()

        with allure.step("验证并发访问结果"):
            assert len(results) == thread_count, f"期望{thread_count}个结果，实际{len(results)}个"

            success_count = sum(1 for result in results if result.get("success", False))
            success_rate = success_count / thread_count

            allure.attach(
                f"并发请求数: {thread_count}, 成功数: {success_count}, 成功率: {success_rate:.2%}",
                "并发测试结果",
                allure.attachment_type.TEXT
            )

            # 至少80%的请求应该成功
            assert success_rate >= 0.8, f"并发访问成功率过低: {success_rate:.2%}"

            logger.info(f"并发访问测试完成，成功率: {success_rate:.2%}")

    @allure.story("性能测试")
    @allure.severity(allure.severity_level.NORMAL)
    def test_performance(self, req, headers):
        """测试接口性能"""
        import time

        project_api = ProjectAPI(req)
        payload = {"pageIndex": 1, "pageSize": 20}

        # 执行多次请求测试性能
        response_times = []
        test_count = 5

        with allure.step(f"执行{test_count}次性能测试"):
            for i in range(test_count):
                start_time = time.time()
                response = project_api.get_project_card_list(payload, headers)
                end_time = time.time()

                response_time = end_time - start_time
                response_times.append(response_time)

                AssertUtil.assert_status_code_200(response)
                logger.info(f"第{i+1}次请求响应时间: {response_time:.3f}秒")

        with allure.step("分析性能数据"):
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)

            performance_report = f"""
            性能测试报告:
            - 测试次数: {test_count}
            - 平均响应时间: {avg_time:.3f}秒
            - 最大响应时间: {max_time:.3f}秒
            - 最小响应时间: {min_time:.3f}秒
            """

            allure.attach(
                performance_report,
                "性能测试报告",
                allure.attachment_type.TEXT
            )

            # 性能要求：平均响应时间不超过5秒
            assert avg_time < 5.0, f"平均响应时间过长: {avg_time:.3f}秒"

            logger.info(f"性能测试完成，平均响应时间: {avg_time:.3f}秒")





    # def test_api_response_has_value():
    #     # 发送API请求
    #     response = requests.get('https://api.example.com/endpoint')
    #     response_data = response.json()
        
    #     # 断言特定字段存在且有值
    #     assert 'target_field' in response_data, "响应中缺少target_field字段"
    #     assert response_data['target_field'] is not None, "target_field字段值为null"
        
    #     # 根据字段类型进行更精确的断言
    #     if isinstance(response_data['target_field'], str):
    #         assert response_data['target_field'].strip() != "", "target_field字段为空字符串"
    #     elif isinstance(response_data['target_field'], list):
    #         assert len(response_data['target_field']) > 0, "target_field字段为空数组"
    #     elif isinstance(response_data['target_field'], dict):
    #         assert response_data['target_field'], "target_field字段为空对象"
    #     # 其他类型检查...

    project_id = project_hour_data["project_config"]["default_project_id"]
    @allure.story("访问项目概览-项目工时指标")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_project_hour(self, req, headers, project_hour_data):
        """测试访问项目概览-项目工时指标"""
        project_id = project_hour_data["project_config"]["default_project_id"]
        with allure.step(f"准备项目ID: {project_id}"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_hour(project_id, headers)

        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)

            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确" 