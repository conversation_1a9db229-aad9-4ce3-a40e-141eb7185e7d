"""
数据库操作接口定义
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session


class IDBUtil(ABC):
    """PyMySQL数据库操作接口"""
    
    @abstractmethod
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        pass
    
    @abstractmethod
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新SQL"""
        pass
    
    @abstractmethod
    def execute_batch(self, sql: str, params_list: List[tuple]) -> int:
        """批量执行SQL"""
        pass
    
    @abstractmethod
    def transaction(self):
        """事务上下文管理器"""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """关闭连接"""
        pass


class ISQLAlchemyUtil(ABC):
    """SQLAlchemy数据库操作接口"""
    
    @abstractmethod
    def get_engine(self) -> Engine:
        """获取数据库引擎"""
        pass
    
    @abstractmethod
    def get_session(self) -> Session:
        """获取数据库会话"""
        pass
    
    @abstractmethod
    def execute_query(self, sql: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        pass
    
    @abstractmethod
    def execute_update(self, sql: str, params: Dict[str, Any] = None) -> int:
        """执行更新SQL"""
        pass
    
    @abstractmethod
    def session_scope(self):
        """会话上下文管理器"""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """关闭连接"""
        pass
