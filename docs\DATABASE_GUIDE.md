# 数据库访问层使用指南

## 📋 概述

RPM自动化测试框架提供了灵活强大的数据库访问层，支持PyMySQL和SQLAlchemy两种访问方式，采用接口与实现分离的设计模式，便于扩展和测试。

## 🏗 架构设计

### 核心组件

```
数据库访问层架构
├── 接口层 (Interface)
│   ├── IDBUtil - PyMySQL接口
│   └── ISQLAlchemyUtil - SQLAlchemy接口
├── 实现层 (Implementation)
│   ├── DBUtil - PyMySQL实现
│   └── SQLAlchemyUtil - SQLAlchemy实现
├── 工厂层 (Factory)
│   └── DBFactory - 统一创建和管理
├── DAO层 (Data Access Object)
│   ├── BaseDAO - 基础DAO抽象类
│   ├── PyMySQLDAO - PyMySQL基础DAO
│   ├── SQLAlchemyDAO - SQLAlchemy基础DAO
│   └── 具体业务DAO (UserDAO, ProjectDAO等)
└── 模型层 (Model)
    ├── BaseModel - 基础模型类
    └── 具体业务模型 (User, Project等)
```

## 🚀 快速开始

### 基础导入

```python
from common.database import DBFactory
from common.core.config_manager import ConfigManager
from common.core.logger import Logger
from common.core.exceptions import DatabaseError, NetworkError

logger = Logger().get_logger()
config = ConfigManager()
```

### PyMySQL方式

```python
# 创建PyMySQL数据库工具
db_util = DBFactory.create_db_util("pymysql")

# 执行查询
users = db_util.execute_query("SELECT * FROM sys_user WHERE status = %s", ("active",))

# 执行更新
affected_rows = db_util.execute_update(
    "UPDATE sys_user SET last_login = NOW() WHERE id = %s", 
    (user_id,)
)

# 关闭连接
db_util.close()
```

### SQLAlchemy方式

```python
# 创建SQLAlchemy数据库工具
db_util = DBFactory.create_sqlalchemy_util()

# 使用会话
with db_util.session_scope() as session:
    users = session.query(User).filter(User.status == 'active').all()
    
    # 创建新用户
    new_user = User(username="test", email="<EMAIL>")
    session.add(new_user)
    # 自动提交和关闭
```

## 🔧 详细配置

### 数据库配置

在 `config/config.yaml` 中配置数据库连接：

```yaml
database:
  host: "localhost"
  port: 3306
  username: "your_username"
  password: "your_password"
  database: "your_database"
  charset: "utf8mb4"
  
  # 连接池配置
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600
```

### 环境变量覆盖

```bash
export DATABASE_HOST="prod-db.example.com"
export DATABASE_USERNAME="prod_user"
export DATABASE_PASSWORD="prod_password"
```

## 🛠 高级用法

### DAO层使用

```python
from dao.user_dao import UserDAO

# 创建DAO实例
user_dao = UserDAO()

# 创建用户
user = user_dao.create_user(username="test", email="<EMAIL>")

# 查询用户
user = user_dao.find_by_username("test")
users = user_dao.find_active_users()

# 更新用户
user.status = 'inactive'
updated_user = user_dao.update(user)

# 删除用户
success = user_dao.delete(user)
```

### 事务管理

```python
# SQLAlchemy事务
with db_util.session_scope() as session:
    try:
        # 多个操作在同一事务中
        user = User(username="test")
        session.add(user)
        session.flush()  # 获取ID但不提交
        
        profile = UserProfile(user_id=user.id, name="Test User")
        session.add(profile)
        # 自动提交
    except Exception:
        # 自动回滚
        raise

# PyMySQL事务
db_util.begin_transaction()
try:
    db_util.execute_update("INSERT INTO users ...")
    db_util.execute_update("INSERT INTO profiles ...")
    db_util.commit_transaction()
except Exception:
    db_util.rollback_transaction()
    raise
```

## 📈 最佳实践

### 1. 选择合适的访问方式

**使用PyMySQL的场景**:
- 简单的CRUD操作
- 性能要求极高
- 复杂的SQL查询
- 数据迁移脚本

**使用SQLAlchemy的场景**:
- 复杂的业务逻辑
- 需要关系映射
- 团队协作开发
- 跨数据库兼容

### 2. 资源管理

```python
# 推荐做法 - 使用上下文管理器
with db_util.session_scope() as session:
    # 数据库操作
    pass  # 自动提交和关闭

# 推荐做法 - 使用try-finally
db_util = DBFactory.create_db_util()
try:
    # 数据库操作
    pass
finally:
    db_util.close()

# 测试结束时清理所有连接
DBFactory.close_all()
```

### 3. 错误处理

```python
from common.core.exceptions import DatabaseError, NetworkError

try:
    users = db_util.execute_query("SELECT * FROM sys_user")
except DatabaseError as e:
    logger.error(f"数据库查询失败: {e.message}")
    logger.error(f"SQL: {e.sql}")
    logger.error(f"参数: {e.params}")
except NetworkError as e:
    logger.error(f"网络连接失败: {e.message}")
```

### 4. 测试数据管理

```python
# 使用事务回滚进行测试隔离
@pytest.fixture
def db_transaction():
    db_util = DBFactory.create_sqlalchemy_util()
    with db_util.session_scope() as session:
        trans = session.begin()
        try:
            yield session
        finally:
            trans.rollback()

# 使用数据清理fixture
def test_user_creation(test_data_cleaner):
    user_dao = UserDAO()
    user = user_dao.create_user(username="test", email="<EMAIL>")
    
    # 注册清理 - 测试结束后自动删除
    test_data_cleaner['users'].append(user.id)
```

## 🚨 故障排除

### 常见问题

#### 1. 连接失败
**症状**: `DatabaseError: 无法连接到数据库`
**原因**: 数据库配置错误、网络问题或数据库服务未启动
**解决方案**:
```python
# 检查配置
config = ConfigManager()
db_config = config.get('database')
logger.info(f"数据库配置: {SecurityUtil.sanitize_data(db_config)}")

# 测试连接
try:
    db_util = DBFactory.create_db_util("pymysql")
    result = db_util.execute_query("SELECT 1")
    logger.info("数据库连接正常")
except DatabaseError as e:
    logger.error(f"连接失败: {e.message}")
```

#### 2. 导入错误
**症状**: `ModuleNotFoundError: No module named 'common.database'`
**原因**: 项目路径配置错误或依赖包未安装
**解决方案**:
```bash
# 检查项目结构
ls -la common/database/

# 重新安装依赖
pip install -r requirements.txt

# 检查Python路径
python -c "import sys; print(sys.path)"
```

#### 3. 事务问题
**症状**: 数据不一致、死锁或事务超时
**原因**: 事务管理不当或长时间持有锁
**解决方案**:
```python
# 正确的事务使用
with db_util.session_scope() as session:
    try:
        # 尽快完成操作
        user = session.query(User).filter_by(id=user_id).first()
        user.status = 'updated'
        # 自动提交
    except Exception as e:
        # 自动回滚
        logger.error(f"事务失败: {e}")
        raise
```

#### 4. 性能问题
**症状**: 查询响应慢或连接池耗尽
**原因**: SQL查询未优化或连接未正确释放
**解决方案**:
```python
# 使用连接池监控
health_status = DBFactory.health_check_all()
logger.info(f"连接池状态: {health_status}")

# 确保连接释放
try:
    db_util = DBFactory.create_db_util()
    # 数据库操作
finally:
    db_util.close()  # 重要：释放连接
```

### 调试技巧

```python
# 启用SQL日志
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# 查看连接池状态
from common.database import DBFactory
status = DBFactory.health_check_all()
print(f"活跃连接: {status.get('active_connections', 0)}")
print(f"空闲连接: {status.get('idle_connections', 0)}")
```

## 🔗 相关文档

- [项目README](../README.md) - 项目总体介绍和快速开始
- [钉钉通知配置](./DINGTALK_SETUP.md) - 钉钉通知功能配置指南
- [配置文件示例](../config/config.yaml.example) - 完整配置文件示例

## 📚 代码示例

完整的使用示例请参考：
- [数据库使用示例](../examples/database_usage_examples.py)
- [DAO层实现](../dao/)
- [模型定义](../models/)

---

*最后更新: 2025-07-28*
*版本: v2.1.0 - 模块化架构重构版本*
*兼容性: Python 3.7+, MySQL 5.7+*

## 📋 更新日志

### v2.1.0 (2025-07-28)
- ✅ 重构为模块化架构，接口与实现分离
- ✅ 统一使用 `common.database` 模块导入
- ✅ 增强配置管理，支持 `ConfigManager`
- ✅ 完善异常处理，使用具体异常类型
- ✅ 优化日志记录，使用 `common.core.logger`
- ✅ 清理重复内容，完善故障排除指南

### v2.0.0 (2025-07-25)
- ✅ 新增SQLAlchemy支持
- ✅ 实现工厂模式统一管理
- ✅ 添加DAO层和模型层
- ✅ 支持多种数据库访问方式
