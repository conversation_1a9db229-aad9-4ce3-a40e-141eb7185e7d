import allure
import pytest
from common.apis.project_api import ProjectAPI
from common.http.assert_util import AssertUtil
from common.core.logger import Logger

# 初始化日志记录器
logger = Logger().get_logger()

@allure.feature("项目管理")
class TestProject:
    
    @allure.story("合同数据迁移到项目")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.critical
    def test_project_data_migration(self, req, headers):
        """测试合同数据迁移到项目功能"""
        with allure.step("执行合同数据迁移"):
            project_api = ProjectAPI(req)
            response = project_api.project_data_migration(headers)
        
        with allure.step("验证迁移结果"):
            AssertUtil.assert_response_success(response)
            # 验证响应中包含迁移相关信息
            AssertUtil.assert_response_contains_field(response, "data")
            
            # 可选：验证迁移后的项目卡片列表不为空
            with allure.step("验证迁移后项目卡片列表"):
                payload = {"pageIndex": 1, "pageSize": 10}
                list_response = project_api.get_project_card_list(payload, headers)
                AssertUtil.assert_response_success(list_response)
                
                # 记录项目数量
                data = list_response.json().get("data", {})
                total = data.get("totalCount", 0)
                logger.info(f"迁移后项目卡片列表 - 总数: {total}")
                allure.attach(str(total), "迁移后项目总数", allure.attachment_type.TEXT)
    
    @allure.story("获取项目卡片列表")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_project_card_list(self, req, headers):
        """测试获取项目卡片列表"""
        with allure.step("准备查询参数"):
            payload = {
                "pageIndex": 1,
                "pageSize": 20
            }
        
        with allure.step("发送获取项目卡片列表请求"):
            project_api = ProjectAPI(req)
            response = project_api.get_project_card_list(payload, headers)
        
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)
            
            # 验证响应结构
            response_data = response.json()
            assert "data" in response_data, "响应中缺少data字段"
            
            data = response_data["data"]
            assert "records" in data, "响应中缺少records字段"
            assert "totalCount" in data, "响应中缺少totalCount字段"
            assert "pageTotal" in data, "响应中缺少pageTotal字段"
            
            # 验证卡片数据结构
            records = data["records"]
            if records:
                first_record = records[0]
                required_fields = [
                    "projectId", "projectName", "projectStatus", "projectManager",
                    "contractParty", "contractPartyId", "contactNo"
                ]
                for field in required_fields:
                    assert field in first_record, f"项目卡片缺少{field}字段"
                
                # 记录项目信息
                logger.info(f"项目卡片列表 - 总数: {data['totalCount']}, 当前页记录数: {len(records)}")
                allure.attach(
                    f"总数: {data['totalCount']}, 当前页: {len(records)}",
                    "项目卡片统计",
                    allure.attachment_type.TEXT
                )
            else:
                logger.info("项目卡片列表为空")
    
    @allure.story("项目卡片列表分页测试")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.parametrize("page_size", [5, 10, 20])
    def test_get_project_card_list_pagination(self, req, headers, page_size):
        """测试项目卡片列表分页功能"""
        with allure.step(f"使用分页大小{page_size}获取项目卡片"):
            payload = {
                "pageIndex": 1,
                "pageSize": page_size
            }
            
            project_api = ProjectAPI(req)
            response = project_api.get_project_card_list(payload, headers)
        
        with allure.step("验证分页响应"):
            AssertUtil.assert_response_success(response)
            
            data = response.json()["data"]
            records = data["records"]
            
            # 验证返回的记录数不超过请求的分页大小
            assert len(records) <= page_size, f"返回记录数({len(records)})超过分页大小({page_size})"
            
            logger.info(f"分页测试 - 请求大小: {page_size}, 实际返回: {len(records)}")
    
    @allure.story("项目卡片详细信息验证")
    @allure.severity(allure.severity_level.NORMAL)
    def test_project_card_detail_validation(self, req, headers):
        """测试项目卡片详细信息验证"""
        with allure.step("获取项目卡片列表"):
            payload = {"pageIndex": 1, "pageSize": 10}
            project_api = ProjectAPI(req)
            response = project_api.get_project_card_list(payload, headers)
            
            AssertUtil.assert_response_success(response)
            records = response.json()["data"]["records"]
            
            if not records:
                pytest.skip("没有项目卡片数据，跳过详细验证")
        
        with allure.step("验证项目卡片详细字段"):
            first_card = records[0]
            
            # 验证项目基础信息
            assert first_card["projectId"], "项目ID不能为空"
            assert first_card["projectName"], "项目名称不能为空"
            assert first_card["projectStatus"] in ["runing", "completed", "pending", "cancelled"], "项目状态值无效"
            
            # 验证合同相关信息
            if first_card.get("contractParty"):
                assert first_card["contractPartyId"], "合同方ID不能为空"
            
            # 验证数值字段类型
            numeric_fields = ["adjustSubjectNum", "contractSubjectNum", "planCenterNum"]
            for field in numeric_fields:
                if first_card.get(field) is not None:
                    assert isinstance(first_card[field], (int, float)), f"{field}应为数值类型"
            
            # 验证日期字段格式
            date_fields = ["projectStartDate", "projectEndDate", "contractBeginDate", "contractEndDate"]
            for field in date_fields:
                if first_card.get(field):
                    # 简单验证日期格式包含年份
                    assert "20" in str(first_card[field]), f"{field}日期格式可能有误"
            
            logger.info(f"项目卡片验证完成 - 项目ID: {first_card['projectId']}, 项目名称: {first_card['projectName']}")
