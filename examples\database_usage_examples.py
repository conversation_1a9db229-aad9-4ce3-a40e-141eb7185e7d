"""
数据库访问层使用示例
演示PyMySQL和SQLAlchemy两种方式的使用
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from common.database.db_factory import DBFactory
from common.core.config_manager import ConfigManager
from dao.user_dao import UserDAO, UserDAOPyMySQL
from dao.project_dao import ProjectDAO, ProjectDAOPyMySQL
from models.user import User
from models.project import Project

def example_pymysql_usage():
    """PyMySQL使用示例"""
    print("=" * 50)
    print("PyMySQL 使用示例")
    print("=" * 50)
    
    # 1. 直接使用DBUtil
    db_util = DBFactory.create_db_util("pymysql")
    
    try:
        # 查询示例
        users = db_util.execute_query(
            "SELECT * FROM sys_user WHERE status = %s LIMIT %s", 
            ('active', 10)
        )
        print(f"查询到 {len(users)} 个用户")
        
        # 插入示例
        user_data = {
            'username': 'test_user_pymysql',
            'email': '<EMAIL>',
            'status': 'active',
            'is_active': True
        }
        
        # 使用事务
        with db_util.transaction() as trans:
            connection = trans.connection
            with connection.cursor() as cursor:
                sql = """
                INSERT INTO sys_user (username, email, status, is_active) 
                VALUES (%(username)s, %(email)s, %(status)s, %(is_active)s)
                """
                cursor.execute(sql, user_data)
                user_id = cursor.lastrowid
                print(f"创建用户成功，ID: {user_id}")
        
        # 2. 使用DAO
        user_dao = UserDAOPyMySQL()
        
        # 查找用户
        user = user_dao.find_by_username('test_user_pymysql')
        if user:
            print(f"找到用户: {user['username']} ({user['email']})")
        
        # 创建新用户
        new_user_id = user_dao.create_user(
            username='dao_user_pymysql',
            email='<EMAIL>',
            description='通过DAO创建的用户'
        )
        print(f"DAO创建用户成功，ID: {new_user_id}")
        
        # 统计用户数
        user_count = user_dao.count()
        print(f"总用户数: {user_count}")
        
    finally:
        db_util.close()

def example_sqlalchemy_usage():
    """SQLAlchemy使用示例"""
    print("=" * 50)
    print("SQLAlchemy 使用示例")
    print("=" * 50)
    
    # 1. 直接使用SQLAlchemyUtil
    db_util = DBFactory.create_sqlalchemy_util()
    
    try:
        # 确保表存在
        db_util.create_all()
        
        # Core方式查询
        users = db_util.execute_query(
            "SELECT * FROM sys_user WHERE status = :status LIMIT :limit",
            {'status': 'active', 'limit': 10}
        )
        print(f"Core查询到 {len(users)} 个用户")
        
        # ORM方式操作
        with db_util.session_scope() as session:
            # 创建用户
            user = User(
                username='test_user_sqlalchemy',
                email='<EMAIL>',
                status='active',
                is_active=True,
                description='通过SQLAlchemy ORM创建'
            )
            session.add(user)
            session.flush()  # 获取ID
            print(f"ORM创建用户成功，ID: {user.id}")
            
            # 查询用户
            found_user = session.query(User).filter(
                User.username == 'test_user_sqlalchemy'
            ).first()
            if found_user:
                print(f"ORM找到用户: {found_user.username} ({found_user.email})")
            
            # 创建项目
            project = Project(
                name='测试项目',
                code='TEST_PROJ_001',
                description='SQLAlchemy测试项目',
                create_user_id=user.id
            )
            session.add(project)
            session.flush()
            print(f"ORM创建项目成功，ID: {project.id}")
        
        # 2. 使用DAO
        user_dao = UserDAO()
        project_dao = ProjectDAO()
        
        # 查找用户
        user = user_dao.find_by_username('test_user_sqlalchemy')
        if user:
            print(f"DAO找到用户: {user.username} ({user.email})")
            
            # 查找用户的项目
            user_projects = project_dao.find_by_user(user.id)
            print(f"用户 {user.username} 有 {len(user_projects)} 个项目")
        
        # 创建新用户和项目
        new_user = user_dao.create_user(
            username='dao_user_sqlalchemy',
            email='<EMAIL>',
            description='通过DAO创建的SQLAlchemy用户'
        )
        print(f"DAO创建用户: {new_user.username} (ID: {new_user.id})")
        
        new_project = project_dao.create_project(
            name='DAO测试项目',
            code='DAO_PROJ_001',
            create_user_id=new_user.id,
            description='通过DAO创建的项目'
        )
        print(f"DAO创建项目: {new_project.name} (ID: {new_project.id})")
        
        # 查询统计
        active_users = user_dao.find_active_users()
        active_projects = project_dao.find_active_projects()
        print(f"激活用户数: {len(active_users)}")
        print(f"激活项目数: {len(active_projects)}")
        
    finally:
        db_util.close()

def example_mixed_usage():
    """混合使用示例"""
    print("=" * 50)
    print("混合使用示例")
    print("=" * 50)
    
    # 可以在同一个应用中同时使用两种方式
    pymysql_dao = UserDAOPyMySQL()
    sqlalchemy_dao = UserDAO()
    
    try:
        # PyMySQL方式查询
        pymysql_users = pymysql_dao.find_active_users()
        print(f"PyMySQL查询到 {len(pymysql_users)} 个激活用户")
        
        # SQLAlchemy方式查询
        sqlalchemy_users = sqlalchemy_dao.find_active_users()
        print(f"SQLAlchemy查询到 {len(sqlalchemy_users)} 个激活用户")
        
        # 数据一致性验证
        if len(pymysql_users) == len(sqlalchemy_users):
            print("✅ 两种方式查询结果一致")
        else:
            print("❌ 两种方式查询结果不一致")
    
    finally:
        # 清理资源
        DBFactory.close_all()

def example_performance_comparison():
    """性能对比示例"""
    print("=" * 50)
    print("性能对比示例")
    print("=" * 50)
    
    import time
    
    # PyMySQL性能测试
    start_time = time.time()
    db_util_pymysql = DBFactory.create_db_util("pymysql")
    
    for i in range(100):
        users = db_util_pymysql.execute_query(
            "SELECT * FROM sys_user WHERE status = %s LIMIT 10", 
            ('active',)
        )
    
    pymysql_time = time.time() - start_time
    db_util_pymysql.close()
    
    # SQLAlchemy性能测试
    start_time = time.time()
    db_util_sqlalchemy = DBFactory.create_sqlalchemy_util()
    
    for i in range(100):
        users = db_util_sqlalchemy.execute_query(
            "SELECT * FROM sys_user WHERE status = :status LIMIT 10",
            {'status': 'active'}
        )
    
    sqlalchemy_time = time.time() - start_time
    db_util_sqlalchemy.close()
    
    print(f"PyMySQL 100次查询耗时: {pymysql_time:.3f}秒")
    print(f"SQLAlchemy 100次查询耗时: {sqlalchemy_time:.3f}秒")
    print(f"性能比较: SQLAlchemy是PyMySQL的 {sqlalchemy_time/pymysql_time:.2f} 倍")

if __name__ == "__main__":
    try:
        # 运行所有示例
        example_pymysql_usage()
        print("\n")
        
        example_sqlalchemy_usage()
        print("\n")
        
        example_mixed_usage()
        print("\n")
        
        example_performance_comparison()
        
    except Exception as e:
        print(f"示例运行失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 确保清理所有连接
        DBFactory.close_all()
        print("\n所有数据库连接已清理")
