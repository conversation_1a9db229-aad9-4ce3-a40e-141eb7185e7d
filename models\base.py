"""
基础模型类定义
"""
from datetime import datetime
from typing import Dict, Any
from sqlalchemy import <PERSON>umn, Integer, DateTime, func
from sqlalchemy.ext.declarative import declared_attr
from models import Base

class BaseModel(Base):
    """基础模型类，包含通用字段和方法"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    @declared_attr
    def __tablename__(cls):
        """自动生成表名"""
        return cls.__name__.lower()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建对象"""
        # 过滤掉不存在的字段
        valid_fields = {c.name for c in cls.__table__.columns}
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return cls(**filtered_data)
    
    def update_from_dict(self, data: Dict[str, Any]):
        """从字典更新对象属性"""
        valid_fields = {c.name for c in self.__table__.columns}
        for key, value in data.items():
            if key in valid_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"