import allure
import pytest
from common.apis.subject_api import SubjectAPI
from common.http.assert_util import AssertUtil
from common.core.exceptions import APIRequestError, ValidationError
from common.core.logger import Logger

# 初始化日志记录器
logger = Logger().get_logger()

@allure.feature("受试者管理")
class TestSubject:
    
    @allure.story("创建受试者 - 完整流程")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.parametrize("gender", ["female", "male"])
    def test_create_subject_full_flow(self, req, headers, subject_data, gender):
        """测试创建受试者的完整流程"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        with allure.step(f"生成{gender}受试者数据"):
            payload = subject_api.generate_subject_data(project_id, gender, headers)
            
            # 验证必需字段是否都已获取
            required_fields = ["deptId", "projectCenterId", "visitPlanId"]
            for field in required_fields:
                if not payload.get(field):
                    pytest.fail(f"依赖数据获取失败: {field} 为空")
        
        with allure.step("发送创建受试者请求"):
            response = subject_api.create_subject(payload, headers)
        
        with allure.step("验证创建结果并获取受试者ID"):
            try:
                AssertUtil.assert_response_success(response)
                
                # 验证响应数据
                response_data = response.json()
                logger.info(f"创建受试者响应: {response_data}")
                assert response_data["message"] == "请求成功", f"创建失败"
                
                # 由于创建接口不返回受试者ID，从列表中获取第一个受试者ID
                with allure.step("从受试者列表获取ID"):
                    subject_id = subject_api.get_first_subject_id(project_id, headers)
                    if subject_id:
                        allure.attach(str(subject_id), "获取的受试者ID", allure.attachment_type.TEXT)
                        logger.info(f"获取到受试者ID: {subject_id}")
                    else:
                        logger.warning("受试者列表为空，无法获取受试者ID")
                
            except (ValidationError, APIRequestError) as e:
                pytest.fail(f"创建受试者失败: {e.message}")
    
    @allure.story("查询受试者列表")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_subject_list(self, req, headers, subject_data):
        """测试查询受试者列表"""
        subject_api = SubjectAPI(req)
        
        with allure.step("准备查询参数"):
            payload = subject_data["get_subject_list"]["valid_payload"]
        
        with allure.step("发送查询受试者列表请求"):
            response = subject_api.get_subject_list(payload, headers)
        
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)
            
            # 验证列表结构
            response_data = response.json()["data"]
            assert "subjects" in response_data, "响应中缺少subjects字段"
            assert "records" in response_data["subjects"], "响应中缺少records字段"
    
    @allure.story("获取受试者详情")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_subject_detail_from_list(self, req, headers, subject_data):
        """测试从列表中获取受试者详情"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        with allure.step("获取受试者ID"):
            subject_id = subject_api.get_first_subject_id(project_id, headers)
            if not subject_id:
                pytest.skip("受试者列表为空，跳过详情测试")
        
        with allure.step(f"获取受试者详情 ID: {subject_id}"):
            detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            AssertUtil.assert_response_success(detail_response)
            
            # 验证详情数据
            detail_data = detail_response.json()["data"]
            assert detail_data["id"], "受试者ID不能为空"
            assert detail_data["randomNo"], "随机编号不能为空"
            assert detail_data["gender"] in ["男", "女"], "性别值无效"
    
    @allure.story("创建后立即获取详情")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_create_and_get_subject_detail(self, req, headers, subject_data):
        """测试创建受试者后立即获取详情"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        with allure.step("创建受试者"):
            payload = subject_api.generate_subject_data(project_id, "female", headers)
            create_response = subject_api.create_subject(payload, headers)
            AssertUtil.assert_response_success(create_response)
            
            # 由于创建接口不返回受试者ID，从列表中获取第一个受试者ID
            subject_id = subject_api.get_first_subject_id(project_id, headers)
            if not subject_id:
                pytest.fail("无法获取新创建的受试者ID")
        
        with allure.step(f"获取新创建的受试者详情 ID: {subject_id}"):
            detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            AssertUtil.assert_response_success(detail_response)
            
            # 验证详情数据有效性
            detail_data = detail_response.json()["data"]
            
            assert str(detail_data["id"]) == str(subject_id), "受试者ID不匹配"
            assert detail_data["gender"] in ["男", "女"], "性别信息无效"
    
    @allure.story("更新受试者信息")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.parametrize("test_scenario", [
        {"name": "更新性别", "updates": {"gender": "male"}},
        {"name": "更新日期", "updates": {"filterDate": "2025-08-01", "knowDate": "2025-08-01"}},
        {"name": "完整更新", "updates": {"gender": "female", "filterDate": "2025-09-01", "knowDate": "2025-09-01"}}
    ])
    def test_update_subject(self, req, headers, subject_data, test_scenario):
        """测试更新受试者信息"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        # 先创建一个受试者
        with allure.step("创建测试受试者"):
            payload = subject_api.generate_subject_data(project_id, "female", headers)
            create_response = subject_api.create_subject(payload, headers)
            AssertUtil.assert_response_success(create_response)
            
            # 由于创建接口不返回受试者ID，从列表中获取第一个受试者ID
            subject_id = subject_api.get_first_subject_id(project_id, headers)
            if not subject_id:
                pytest.fail("无法获取新创建的受试者ID")
        
        with allure.step("获取受试者详情"):
            detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            AssertUtil.assert_response_success(detail_response)
            subject_detail = detail_response.json()["data"]
        
        with allure.step(f"执行更新操作: {test_scenario['name']}"):
            update_data = subject_api.generate_update_data(
                subject_detail,
                gender=test_scenario["updates"].get("gender"),
                filter_date=test_scenario["updates"].get("filterDate"),
                know_date=test_scenario["updates"].get("knowDate")
            )
            
            update_response = subject_api.update_subject(update_data, headers)
            AssertUtil.assert_response_success(update_response)
        
        with allure.step("验证更新结果"):
            # 获取更新后的详情验证
            updated_detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            AssertUtil.assert_response_success(updated_detail_response)
            
            updated_data = updated_detail_response.json()["data"]
            
            # 验证更新的字段
            for field, expected_value in test_scenario["updates"].items():
                if field == "gender":
                    assert updated_data["gender"] == expected_value, f"性别更新失败，期望: {expected_value}, 实际: {updated_data['gender']}"
                elif field in ["filterDate", "knowDate"]:
                    # 日期字段可能包含时间部分，只验证日期部分
                    actual_date = updated_data.get(field, "").split("T")[0]
                    assert actual_date == expected_value, f"{field}更新失败，期望: {expected_value}, 实际: {actual_date}"
    
    @allure.story("删除受试者")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_delete_subject(self, req, headers, subject_data):
        """测试删除受试者"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        # 先创建一个受试者用于删除
        with allure.step("创建测试受试者"):
            payload = subject_api.generate_subject_data(project_id, "male", headers)
            create_response = subject_api.create_subject(payload, headers)
            AssertUtil.assert_response_success(create_response)
            
            # 由于创建接口不返回受试者ID，从列表中获取第一个受试者ID
            subject_id = subject_api.get_first_subject_id(project_id, headers)
            if not subject_id:
                pytest.fail("无法获取新创建的受试者ID")
        
        with allure.step(f"删除受试者 ID: {subject_id}"):
            delete_response = subject_api.delete_subject(subject_id, headers)
            AssertUtil.assert_response_success(delete_response)
        
        with allure.step("验证删除结果"):
            # 尝试获取已删除的受试者详情，应该失败
            detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            # 根据实际API行为，删除后可能返回404或其他错误码
            assert detail_response.status_code != 200 or detail_response.json().get("code") != "200", "受试者删除后仍能获取详情"
    
    @allure.story("依赖数据获取测试")
    @allure.severity(allure.severity_level.NORMAL)
    def test_get_dependencies(self, req, headers, subject_data):
        """测试获取受试者创建依赖数据"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        with allure.step("获取项目中心列表"):
            center_response = subject_api.project_api.get_project_center_list(project_id, headers)
            AssertUtil.assert_response_success(center_response)
            
            center_data = center_response.json()["data"]
            assert "records" in center_data, "中心列表响应格式错误"
            assert len(center_data["records"]) > 0, "中心列表为空"
        
        with allure.step("获取访视方案列表"):
            visit_response = subject_api.visitingplan_api.get_visit_solution_list(project_id, headers)
            AssertUtil.assert_response_success(visit_response)
            
            visit_data = visit_response.json()["data"]
            assert len(visit_data) > 0, "访视方案列表为空"
        
        with allure.step("获取完整依赖数据"):
            dependencies = subject_api.get_subject_dependencies(project_id, headers)
            
            required_keys = ["deptId", "projectCenterId", "visitPlanId"]
            for key in required_keys:
                assert key in dependencies, f"依赖数据缺少{key}"
                assert dependencies[key], f"依赖数据{key}为空"
    
    @allure.story("批量创建受试者")
    @allure.severity(allure.severity_level.NORMAL)
    def test_batch_create_subjects(self, req, headers, subject_data):
        """测试批量创建受试者"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        with allure.step("批量创建3个受试者"):
            gender_ratio = {"female": 0.7, "male": 0.3}
            results = subject_api.batch_create_subjects(
                project_id=project_id,
                count=3,
                gender_ratio=gender_ratio,
                headers=headers
            )
        
        with allure.step("验证批量创建结果"):
            assert len(results) == 3, "创建数量不匹配"
            
            success_count = sum(1 for r in results if r["success"])
            assert success_count > 0, "没有成功创建的受试者"
            
            # 验证性别分布
            female_count = sum(1 for r in results if r["gender"] == "female")
            male_count = sum(1 for r in results if r["gender"] == "male")
            
            logger.info(f"批量创建结果 - 总数: {len(results)}, 成功: {success_count}, 女性: {female_count}, 男性: {male_count}")
            
            # 添加到Allure报告中
            allure.attach(
                f"总数: {len(results)}, 成功: {success_count}, 女性: {female_count}, 男性: {male_count}",
                "批量创建统计",
                allure.attachment_type.TEXT
            )
    
    @allure.story("完整CRUD流程测试")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_subject_crud_flow(self, req, headers, subject_data):
        """测试受试者完整的CRUD流程"""
        project_id = subject_data["project_config"]["default_project_id"]
        subject_api = SubjectAPI(req)
        
        # 1. 创建受试者
        with allure.step("1. 创建受试者"):
            payload = subject_api.generate_subject_data(project_id, "female", headers)
            create_response = subject_api.create_subject(payload, headers)
            AssertUtil.assert_response_success(create_response)
            
            # 由于创建接口不返回受试者ID，从列表中获取第一个受试者ID
            subject_id = subject_api.get_first_subject_id(project_id, headers)
            if not subject_id:
                pytest.fail("无法获取新创建的受试者ID")
            allure.attach(str(subject_id), "获取的受试者ID", allure.attachment_type.TEXT)
        
        # 2. 查询受试者详情
        with allure.step("2. 查询受试者详情"):
            detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            AssertUtil.assert_response_success(detail_response)
            subject_detail = detail_response.json()["data"]
        
        # 3. 更新受试者信息
        with allure.step("3. 更新受试者信息"):
            update_data = subject_api.generate_update_data(
                subject_detail,
                gender="male",
                filter_date="2025-08-15",
                know_date="2025-08-15"
            )
            update_response = subject_api.update_subject(update_data, headers)
            AssertUtil.assert_response_success(update_response)
        
        # 4. 验证更新结果
        with allure.step("4. 验证更新结果"):
            updated_detail_response = subject_api.get_subject_detail(subject_id, project_id, headers)
            AssertUtil.assert_response_success(updated_detail_response)
            
            updated_data = updated_detail_response.json()["data"]
            assert updated_data["gender"] == "male", "性别更新失败"
        
        # 5. 删除受试者
        with allure.step("5. 删除受试者"):
            delete_response = subject_api.delete_subject(subject_id, headers)
            AssertUtil.assert_response_success(delete_response)
        
        # 6. 验证删除结果
        with allure.step("6. 验证删除结果"):
            detail_after_delete = subject_api.get_subject_detail(subject_id, project_id, headers)
            assert detail_after_delete.status_code != 200 or detail_after_delete.json().get("code") != "200", "受试者删除后仍能获取详情"
