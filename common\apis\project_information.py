"""
项目信息配置API封装
"""

import allure
from common.core.logger import Logger

logger = Logger().get_logger()


class ProjectInformation:
    """项目信息配置API封装类"""

    def __init__(self, request_util):
        self.req = request_util

    @allure.step("新增项目竞争公司")
    def add_project_competitor(self, project_id, headers):
        """新增项目竞争公司"""
        url = "/rpm-api/project/createProjectCompetitor"
        arr = []

        json = {"companyId":"7307686256895782478","centerNumber":3,"firstStartDate":"2025-07-25","currentNumber":1,"endJoinDate":"2025-07-26","recordUpdateDate":"2025-07-25","projectId":project_id,"companyName":"江苏润旭医药科技有限公司"}

        arr.append(json)

        logger.info(f"新增项目竞争公司参数: {arr}")
        response = self.req.send_request(method="post", url=url, json=arr, headers=headers)
        logger.info(f"新增项目竞争公司响应: {response.status_code} - {response.text}")
        return response
    

    @allure.step("保存项目信息配置")
    def save_project_information(self, project_id, headers):
        """保存项目信息配置"""
        url = "/rpm-api/project/edit"
        json = {"projectId":project_id,
                "contractNo":"C101872L",
                "projectSimpleName":"恒瑞_SHR-1139注射液_中重度斑块状银屑病_I期",
                "crtNo":None,
                "projectStatus":"runing",
                "projectStartDate":"2024-04-01T00:00:00+08:00",
                "projectEndDate":"2025-07-01T00:00:00+08:00",
                "plannedCenterCount":5,
                "plannedGroups":65,
                "lastCenterNumber":5,
                "lastEnteredNumber":64,
                "dropoutRate":50,
                "schemeFullName":"健康受试者单次皮下注射或静脉输注和中重度斑块状银屑病患者多次皮下注射SHR-1139注射液的安全性、耐受性、药代动力学和药效学研究——随机、双盲、剂量递增、安慰剂对照I期临床试验",
                "projectOverviewRemark":None,
                "drugDeviceCategory":None,
                "testStagesCode":"5",
                "dosageMethod":None,
                "drugDeviceName":"SHR-1139注射液",
                "scaleCode":"7",
                "productClassCode":"01.Medicine",
                "domainClassCode":"01.Medicine_Non-oncology",
                "domainCode":"01.Medicine_Non-oncology_non-onco.16",
                "contrastClassCode":"01.Medicine_Non-oncology_non-onco.16_16",
                "targetCode":None,
                "indicationCode":["中重度斑块状银屑病"],
                "indicationText":"[\"中重度斑块状银屑病\"]"}
        logger.info(f"保存项目信息配置参数: {json}")
        response = self.req.send_request(method="post", url=url, json=json, headers=headers)
        logger.info(f"保存项目信息配置响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("获取项目竞争公司信息")
    def get_project_competitor_info(self, project_id, headers):
        """获取项目竞争公司信息"""
        url = "/rpm-api/project/projectCompetitorInfo"
        params = {"projectId": project_id}
        logger.info(f"获取项目竞争公司信息参数: {params}")
        response = self.req.send_request(method="get", url=url, params=params, headers=headers)
        logger.info(f"获取项目竞争公司信息响应: {response.status_code} - {response.text}")
        return response
    

    @allure.step("获取项目信息配置")
    def get_project_info(self, project_id, headers):
        '''获取项目信息配置'''
        url = "/rpm-api/project/projectInfo"
        params = {"projectId": project_id}
        logger.info(f"获取项目信息配置参数: {params}")
        response = self.req.send_request(method="get", url=url, params=params, headers=headers)
        logger.info(f"获取项目信息配置响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("删除项目竞争公司")
    def delete_project_competitor(self, company_id, headers):
        '''删除项目竞争公司'''
        url = "/rpm-api/project/delProjectCompetitor"
        params = {"id": company_id}
        logger.info(f"删除项目竞争公司参数: {params}")
        response = self.req.send_request(method="post", url=url, params=params, headers=headers)
        logger.info(f"删除项目竞争公司响应: {response.status_code} - {response.text}")
        return response

    





        




