# 钉钉通知配置指南

## 📋 概述

RPM自动化测试框架支持测试完成后自动发送报告到钉钉群，提供智能的通知策略和多环境配置支持。

## 🚀 快速配置

### 1. 创建钉钉机器人

1. 在钉钉群中点击"群设置" → "智能群助手" → "添加机器人"
2. 选择"自定义"机器人
3. 设置机器人名称（如：RPM测试报告）
4. 配置安全设置（推荐使用"加签"方式）
5. 复制生成的webhook地址

### 2. 配置框架

在 `config/config.yaml` 中添加钉钉配置：

```yaml
# 钉钉通知配置
dingtalk_webhook: "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"
dingtalk_secret: "YOUR_SECRET_KEY"  # 如果启用了加签
dingtalk_enabled: true
dingtalk_at_all: false
dingtalk_at_mobiles: ["13800138000"]  # 需要@的手机号
```

### 3. 验证配置

```python
from common.core.config_manager import ConfigManager
from common.notifications.dingtalk_notifier import DingTalkNotifier

# 验证配置
config = ConfigManager()
notifier = DingTalkNotifier(config)

# 发送测试消息
success = notifier.send_test_message()
if success:
    print("✅ 钉钉配置成功")
else:
    print("❌ 钉钉配置失败")
```

## 🔧 详细配置

### 配置项说明

| 配置项 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `dingtalk_webhook` | string | 是 | 钉钉机器人webhook地址 |
| `dingtalk_secret` | string | 否 | 加签密钥（推荐使用） |
| `dingtalk_enabled` | boolean | 否 | 是否启用钉钉通知，默认true |
| `dingtalk_at_all` | boolean | 否 | 是否@所有人，默认false |
| `dingtalk_at_mobiles` | array | 否 | 需要@的手机号列表 |

### 多环境配置

支持为不同环境配置不同的钉钉群：

```yaml
# config/config_dev.yaml - 开发环境
dingtalk_enabled: false  # 开发环境不发送通知

# config/config_uat.yaml - UAT环境
dingtalk_webhook: "https://oapi.dingtalk.com/robot/send?access_token=UAT_TOKEN"
dingtalk_at_mobiles: ["测试负责人手机号"]

# config/config_prod.yaml - 生产环境
dingtalk_webhook: "https://oapi.dingtalk.com/robot/send?access_token=PROD_TOKEN"
dingtalk_at_all: true  # 生产环境失败时@所有人
```

### 环境变量覆盖

```bash
# 通过环境变量覆盖配置
export DINGTALK_WEBHOOK="https://oapi.dingtalk.com/robot/send?access_token=NEW_TOKEN"
export DINGTALK_ENABLED="true"
export DINGTALK_AT_ALL="false"
```

## 📊 智能通知策略

### 通知规则配置

框架根据测试结果和环境自动决定通知策略：

```yaml
# 在配置文件中设置通知规则
dingtalk_notification_rules:
  dev:
    enabled: false  # 开发环境不发送
  uat:
    enabled: true
    at_all_threshold: 0.7  # 成功率低于70%时@所有人
    at_specific_threshold: 0.8  # 成功率低于80%时@指定人员
    failed_count_threshold: 5  # 失败数超过5个时@人
  prod:
    enabled: true
    at_all_threshold: 0.9  # 生产环境要求更高
    always_notify: true  # 总是发送通知
```

### 实际使用示例

```python
from common.notifications.dingtalk_notifier import DingTalkNotifier

# 自动判断是否需要@人
notifier = DingTalkNotifier(config)
test_summary = notifier.generate_test_report_summary("./allure-results")

# 根据配置规则自动决策
should_at_all = notifier.should_at_all_people(test_summary)
should_at_specific = notifier.should_at_specific_people(test_summary)

logger.info(f"测试结果: 成功率 {test_summary['success_rate']}%")
logger.info(f"通知策略: @所有人={should_at_all}, @指定人员={should_at_specific}")
```

### 环境特定策略

| 环境 | 通知条件 | @人策略 | 备注 |
|------|----------|---------|------|
| dev | 不发送 | - | 开发环境静默 |
| uat | 失败数>5 或 成功率<80% | @测试负责人 | 测试环境监控 |
| stress | 总是发送 | @性能测试负责人 | 压测结果通知 |
| prod | 总是发送 | 成功率<90%时@所有人 | 生产环境严格监控 |

## 🛠 高级用法

### 自定义通知内容

```python
from common.notifications.dingtalk_notifier import DingTalkNotifier
from common.core.config_manager import ConfigManager

config = ConfigManager()
notifier = DingTalkNotifier(config)

# 自定义消息内容
custom_message = {
    "msgtype": "markdown",
    "markdown": {
        "title": "自定义测试报告",
        "text": "## 测试完成\n\n**结果**: 成功\n**详情**: 所有用例通过"
    }
}

success = notifier.send_custom_message(custom_message)
```

### 批量发送

```python
# 发送到多个群
webhooks = [
    "https://oapi.dingtalk.com/robot/send?access_token=TOKEN1",
    "https://oapi.dingtalk.com/robot/send?access_token=TOKEN2"
]

for webhook in webhooks:
    config.set("dingtalk_webhook", webhook)
    notifier = DingTalkNotifier(config)
    notifier.send_test_report_summary(results_dir)
```

## 🔒 安全配置

### 加签验证

推荐使用加签方式提升安全性：

1. 创建机器人时选择"加签"安全设置
2. 复制生成的密钥
3. 在配置中设置 `dingtalk_secret`

```yaml
dingtalk_secret: "SECxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

### IP白名单

如果使用IP白名单，需要将服务器IP添加到白名单中。

### 关键词验证

如果使用关键词验证，确保消息内容包含设置的关键词。

## 🧪 测试和调试

### 手动发送报告

```bash
# 使用脚本手动发送报告
python send_dingtalk_report.py --results-dir ./allure-results

# 预览模式（不实际发送）
python send_dingtalk_report.py --dry-run

# 指定webhook
python send_dingtalk_report.py --webhook "YOUR_WEBHOOK_URL"
```

### 调试模式

```python
from common.core.logger import Logger
from common.notifications.dingtalk_notifier import DingTalkNotifier

# 启用详细日志
logger = Logger().get_logger()
logger.setLevel("DEBUG")

# 创建通知器
notifier = DingTalkNotifier(config)
notifier.debug = True  # 启用调试模式

# 发送测试消息
result = notifier.send_test_message()
```

## 🚨 故障排除

### 常见问题

#### 1. 发送失败："sign not match"
**原因**：加签验证失败
**解决**：检查 `dingtalk_secret` 配置是否正确

#### 2. 发送失败："invalid webhook url"
**原因**：webhook地址无效
**解决**：重新获取机器人webhook地址

#### 3. 消息被拒绝："keywords not in content"
**原因**：消息内容不包含关键词
**解决**：在消息中添加设置的关键词

#### 4. 网络超时
**原因**：网络连接问题
**解决**：检查网络连接，配置代理（如需要）

### 日志分析

```python
# 查看详细的发送日志
from common.core.logger import Logger

logger = Logger().get_logger()

# 日志会包含：
# - 请求URL和参数
# - 响应状态码和内容
# - 错误详情和重试信息
```

## 🔗 相关文档

- [项目README](../README.md) - 项目总体介绍
- [数据库访问指南](./DATABASE_GUIDE.md) - 数据库功能使用指南
- [配置文件示例](../config/config.yaml.example) - 完整配置文件示例
- [钉钉发送脚本](../send_dingtalk_report.py) - 手动发送钉钉报告

## 📁 相关文件

- `common/core/config_manager.py` - 配置管理器实现
- `common/notifications/dingtalk_notifier.py` - 钉钉通知器实现
- `common/core/logger.py` - 日志管理器实现

---

*最后更新: 2025-07-28*
*版本: v2.1.0 - 模块化架构重构版本*

## 📋 更新日志

### v2.1.0 (2025-07-28)
- ✅ 更新所有导入路径为模块化架构
- ✅ 使用 `common.core.config_manager` 统一配置管理
- ✅ 使用 `common.core.logger` 统一日志记录
- ✅ 完善错误处理和调试功能
- ✅ 优化通知策略配置和使用示例
- ✅ 清理重复内容，提升文档质量
