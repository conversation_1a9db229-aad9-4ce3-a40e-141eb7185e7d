"""
项目信息配置API接口测试用例
"""
import allure


from common.apis.project_information import ProjectInformation
from common.http.assert_util import AssertUtil

@allure.feature("项目信息配置API接口")
class TestInformationAPI:
    @allure.story("新增项目竞争公司并获取项目竞争公司信息再删除")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_add_project_competitor(self, req, headers, subject_data):
        """新增项目竞争公司"""
        project_id = subject_data["project_config"]["default_project_id"]
        project_information = ProjectInformation(req)
        company_id = ""

        with allure.step("新增项目竞争公司"):
            response = project_information.add_project_competitor(project_id,headers)

        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)

            # 验证响应结构
            response_data = response.json()
            assert "data" in response_data, "响应中缺少data字段"

            # 验证数据内容
            message = response_data["message"]
            assert message == "请求成功", "响应消息不正确"

        """获取项目竞争公司信息"""
        with allure.step("获取项目竞争公司信息"):
            response = project_information.get_project_competitor_info(project_id,headers)
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)

            response_data = response.json()
            arr = []

            arr.extend(["centerNumber","companyId","companyName","currentNumber","endJoinDate","firstStartDate","id","projectId","recordUpdateDate"])
            for field in arr:
                assert field in response_data["data"][0], f"响应中缺少{field}字段"  

            assert response_data["data"][0]["companyName"] == "江苏润旭医药科技有限公司", "响应消息不正确" 
            assert response_data["message"] == "请求成功", "响应消息不正确"
            company_id =  response_data["data"][0]["id"]
        '''删除项目竞争公司'''
        with allure.step("删除项目竞争公司"):
            response = project_information.delete_project_competitor(company_id,headers)   
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)
            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确"



    
    @allure.story("保存项目信息配置并获取项目信息配置")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_save_project_information(self, req, headers, subject_data):
        """保存项目信息配置"""
        project_id = subject_data["project_config"]["default_project_id"]
        project_information = ProjectInformation(req)

        with allure.step("保存项目信息配置"):
            response = project_information.save_project_information(project_id,headers)


        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)

            # 验证响应结构
            response_data = response.json()
            assert "data" in response_data, "响应中缺少data字段"

            # 验证数据内容
            message = response_data["message"]
            assert message == "请求成功", "响应消息不正确"

        '''获取项目信息配置'''
        with allure.step("获取项目信息配置"):
            response = project_information.get_project_info(project_id,headers)
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)

            response_data = response.json()
            assert response_data["message"] == "请求成功", "响应消息不正确"
            assert response_data["data"]["projectSimpleName"] == "恒瑞_SHR-1139注射液_中重度斑块状银屑病_I期", "响应消息不正确"
    

            
            








        




           

