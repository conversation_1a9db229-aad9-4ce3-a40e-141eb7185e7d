"""
受试者相关API封装
"""
import allure
import time
import random
from datetime import datetime
from common.core.logger import Logger
from common.apis.project_api import ProjectAPI
from common.apis.visitingplan_api import VisitingPlanAPI

logger = Logger().get_logger()

class SubjectAPI:
    """受试者管理API封装类"""
    
    def __init__(self, request_util):
        self.req = request_util
        self.project_api = ProjectAPI(request_util)
        self.visitingplan_api = VisitingPlanAPI(request_util)
    
    @allure.step("添加受试者")
    def create_subject(self, payload, headers):
        """添加受试者"""
        url = "/rpm-api/subject/create"
        logger.info(f"添加受试者请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"添加受试者响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("查询受试者列表")
    def get_subject_list(self, payload, headers):
        """查询受试者列表
        
        Args:
            payload: 请求体参数
                - pageIndex: 页码
                - pageSize: 每页大小
                - projectId: 项目ID
            headers: 请求头
        """
        url = "/rpm-api/subject/list"
        logger.info(f"查询受试者列表请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"查询受试者列表响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("根据ID获取受试者详情")
    def get_subject_detail(self, subject_id, project_id, headers):
        """根据ID获取受试者详情
        
        Args:
            subject_id: 受试者ID
            project_id: 项目ID
            headers: 请求头
        """
        url = "/rpm-api/subject/subjectInfo"
        current_timestamp = int(time.time() * 1000)
        params = {
            "subjectId": subject_id,
            "projectId": project_id,
            "_t": current_timestamp
        }
        logger.info(f"获取受试者详情请求参数: {params}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"获取受试者详情响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("更新受试者信息")
    def update_subject(self, payload, headers):
        """更新受试者信息
        
        Args:
            payload: 更新数据
                - id: 受试者ID
                - randomNo: 随机编号
                - gender: 性别
                - filterDate: 筛选日期
                - knowDate: 知情同意日期
            headers: 请求头
        """
        url = "/rpm-api/subject/update"
        logger.info(f"更新受试者请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"更新受试者响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("删除受试者")
    def delete_subject(self, subject_id, headers):
        """删除受试者
        
        Args:
            subject_id: 受试者ID
            headers: 请求头
        """
        url = "/rpm-api/subject/delete"
        payload = {
            "subjectId": subject_id
        }
        logger.info(f"删除受试者请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"删除受试者响应: {response.status_code} - {response.text}")
        return response
    
    @allure.step("获取受试者创建所需的依赖数据")
    def get_subject_dependencies(self, project_id, headers):
        """获取创建受试者所需的依赖数据"""
        dependencies = {}
        
        # 获取项目中心信息
        with allure.step("获取项目中心信息"):
            center_response = self.project_api.get_project_center_list(project_id, headers)
            if center_response.status_code == 200:
                center_data = center_response.json()
                if center_data.get("code") == "200" and center_data.get("data", {}).get("records"):
                    records = center_data["data"]["records"]
                    if records:
                        first_record = records[0]
                        dependencies["deptId"] = first_record["deptId"]
                        dependencies["projectCenterId"] = first_record["id"]
                        dependencies["centerInfo"] = first_record  # 保存完整信息
                        logger.info(f"获取到中心信息: deptId={dependencies['deptId']}, projectCenterId={dependencies['projectCenterId']}")
                    else:
                        logger.warning("项目中心列表为空")
                else:
                    logger.warning(f"获取项目中心失败: {center_data.get('message', '未知错误')}")
            else:
                logger.error(f"查询项目中心接口失败: {center_response.status_code}")
        
        # 获取访视方案信息
        visit_dependencies = self.visitingplan_api.get_visit_plan_dependencies(project_id, headers)
        dependencies.update(visit_dependencies)
        
        return dependencies
    
    @allure.step("验证项目依赖数据完整性")
    def validate_project_dependencies(self, dependencies, required_fields=None):
        """验证项目依赖数据的完整性
        
        Args:
            dependencies: 依赖数据字典
            required_fields: 必需字段列表，默认为基础字段
            
        Returns:
            bool: 验证是否通过
            
        Raises:
            ValueError: 当必需字段缺失时抛出异常
        """
        if required_fields is None:
            required_fields = ["deptId", "projectCenterId", "visitPlanId"]
        
        missing_fields = []
        for field in required_fields:
            if not dependencies.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            error_msg = f"依赖数据验证失败，缺少必需字段: {missing_fields}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info(f"依赖数据验证通过: {required_fields}")
        return True
    
    @allure.step("生成受试者创建数据")
    def generate_subject_data(self, project_id, gender="female", headers=None):
        """生成创建受试者所需的完整数据
        
        Args:
            project_id: 项目ID
            gender: 性别，female或male
            headers: 请求头
            
        Returns:
            dict: 受试者创建数据
        """
        # 获取依赖数据
        dependencies = self.get_subject_dependencies(project_id, headers)
        
        # 验证依赖数据完整性
        self.validate_project_dependencies(dependencies)
        
        # 生成随机数据
        random_no = str(random.randint(1000, 9999))
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        subject_data = {
            "deptId": dependencies.get("deptId"),
            "projectCenterId": dependencies.get("projectCenterId"),
            "no": random_no,
            "randomNo": random_no,
            "gender": gender,
            "visitPlanId": dependencies.get("visitPlanId"),
            "knowDate": current_date,
            "filterDate": current_date,
            "projectId": project_id
        }
        
        logger.info(f"生成受试者数据: {subject_data}")
        return subject_data
    
    @allure.step("从受试者列表获取第一个受试者ID")
    def get_first_subject_id(self, project_id, headers):
        """从受试者列表获取第一个受试者ID
        
        Args:
            project_id: 项目ID
            headers: 请求头
            
        Returns:
            str: 受试者ID，如果列表为空则返回None
        """
        payload = {
            "pageIndex": 1,
            "pageSize": 20,
            "projectId": project_id
        }
        
        response = self.get_subject_list(payload, headers)
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get("code") == "200":
                subjects = response_data.get("data", {}).get("subjects", {}).get("records", [])
                if subjects:
                    subject_id = subjects[0]["subjectId"]
                    logger.info(f"获取到第一个受试者ID: {subject_id}")
                    return subject_id
                else:
                    logger.warning("受试者列表为空")
                    return None
            else:
                logger.error(f"获取受试者列表失败: {response_data.get('message', '未知错误')}")
                return None
        else:
            logger.error(f"查询受试者列表接口失败: {response.status_code}")
            return None
    
    @allure.step("生成受试者更新数据")
    def generate_update_data(self, subject_detail, gender=None, filter_date=None, know_date=None):
        """根据受试者详情生成更新数据
        
        Args:
            subject_detail: 受试者详情数据
            gender: 新的性别，不传则保持原值
            filter_date: 新的筛选日期，不传则保持原值
            know_date: 新的知情同意日期，不传则保持原值
            
        Returns:
            dict: 更新数据
        """
        # 格式化日期为ISO格式
        def format_date(date_str):
            if date_str:
                # 如果传入的是YYYY-MM-DD格式，转换为ISO格式
                if len(date_str) == 10:
                    return f"{date_str}T00:00:00+08:00"
                return date_str
            return None
        
        update_data = {
            "id": subject_detail["id"],
            "randomNo": subject_detail["randomNo"],
            "gender": gender if gender is not None else subject_detail["gender"],
            "filterDate": format_date(filter_date) if filter_date else subject_detail.get("filterDate"),
            "knowDate": format_date(know_date) if know_date else subject_detail.get("knowDate")
        }
        
        logger.info(f"生成受试者更新数据: {update_data}")
        return update_data
    
    @allure.step("批量创建受试者")
    def batch_create_subjects(self, project_id, count=1, gender_ratio=None, headers=None):
        """批量创建受试者
        
        Args:
            project_id: 项目ID
            count: 创建数量
            gender_ratio: 性别比例，如 {"female": 0.6, "male": 0.4}
            headers: 请求头
            
        Returns:
            list: 创建结果列表
        """
        if gender_ratio is None:
            gender_ratio = {"female": 0.5, "male": 0.5}
        
        results = []
        
        # 先获取一次依赖数据，避免重复调用
        dependencies = self.get_subject_dependencies(project_id, headers)
        self.validate_project_dependencies(dependencies)
        
        for i in range(count):
            # 根据比例确定性别
            import random as rand
            gender = "female" if rand.random() < gender_ratio["female"] else "male"
            
            # 生成数据（复用已获取的依赖数据）
            random_no = str(random.randint(1000, 9999))
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            subject_data = {
                "deptId": dependencies.get("deptId"),
                "projectCenterId": dependencies.get("projectCenterId"),
                "no": random_no,
                "randomNo": random_no,
                "gender": gender,
                "visitPlanId": dependencies.get("visitPlanId"),
                "knowDate": current_date,
                "filterDate": current_date,
                "projectId": project_id
            }
            
            # 创建受试者
            response = self.create_subject(subject_data, headers)
            results.append({
                "index": i + 1,
                "gender": gender,
                "payload": subject_data,
                "response": response,
                "success": response.status_code == 200
            })
        
        logger.info(f"批量创建受试者完成，总数: {count}, 成功: {sum(1 for r in results if r['success'])}")
        return results
