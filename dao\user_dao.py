"""
用户DAO - 用户数据访问对象
"""
from typing import List, Optional, Dict, Any
from .base_dao import SQLAlchemyDAO, PyMySQLDAO
from models.user import User

class UserDAO(SQLAlchemyDAO):
    """用户DAO - SQLAlchemy实现"""
    
    @property
    def table_name(self) -> str:
        return "sys_user"
    
    @property
    def model_class(self):
        return User
    
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        return self.query(username=username)
    
    def find_active_users(self) -> List[User]:
        """查找所有活跃用户"""
        return self.query(status='active')

class UserPyMySQLDAO(PyMySQLDAO):
    """用户DAO - PyMySQL实现"""
    
    @property
    def table_name(self) -> str:
        return "sys_user"
    
    @property
    def model_class(self):
        return User
    
    def find_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名查找用户"""
        sql = f"SELECT * FROM {self.table_name} WHERE username = %s AND deleted = 0"
        results = self.db_util.execute_query(sql, (username,))
        return results[0] if results else None
    
    def find_active_users(self) -> List[Dict[str, Any]]:
        """查找所有活跃用户"""
        sql = f"SELECT * FROM {self.table_name} WHERE status = 'active' AND deleted = 0"
        return self.db_util.execute_query(sql)
