"""
项目管理相关API封装
"""
import allure
from typing import Dict, Any
from ..http.request_util import RequestUtil
from ..core.logger import Logger

logger = Logger().get_logger()

class ProjectAPI:
    """项目管理API封装类"""
    
    def __init__(self, request_util):
        self.req = request_util

    @allure.step("合同数据迁移到项目")
    def project_data_migration(self, headers):
        """合同数据迁移到项目"""
        url = "/rpm-api/project/projectDataMigration"
        logger.info("执行合同数据迁移到项目")
        response = self.req.send_request("POST", url, headers=headers)
        logger.info(f"合同数据迁移响应: {response.status_code} - {response.text}")
        return response

    @allure.step("获取项目卡片列表")
    def get_project_card_list(self, payload, headers):
        """获取项目卡片列表信息
        
        Args:
            payload: 请求体参数
                - pageIndex: 页码
                - pageSize: 每页大小
            headers: 请求头
        """
        url = "/rpm-api/project/cardList"
        logger.info(f"获取项目卡片列表请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"获取项目卡片列表响应: {response.status_code} - {response.text}")
        return response

    @allure.step("查询项目中心列表")
    def get_project_center_list(self, project_id, headers, page_size=99999):
        """查询项目中心列表
        
        Args:
            project_id: 项目ID
            headers: 请求头
            page_size: 分页大小，默认99999获取所有数据
            
        Returns:
            response: HTTP响应对象
        """
        url = "/rpm-api/projectCenter/listSub"
        payload = {
            "projectId": project_id,
            "pageSize": page_size
        }
        logger.info(f"查询项目中心列表请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"查询项目中心列表响应: {response.status_code} - {response.text}")
        return response
