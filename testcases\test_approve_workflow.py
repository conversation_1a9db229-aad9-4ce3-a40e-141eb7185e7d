"""
发布访视方案审批测试用例
基于approve_api.py实现的发布访视方案审批测试
"""
import pytest
import allure
import json
from pathlib import Path
import sys
from common.apis import ApproveAPI
from common.core.logger import Logger

# 初始化日志记录器
logger = Logger().get_logger()


@allure.feature("审批管理")
@allure.story("发布访视方案审批")
class TestApproveWorkflow:
    """发布访视方案审批测试类"""
    
    @pytest.fixture(scope="class")
    def approve_api(self, req):
        """初始化审批API"""
        return ApproveAPI(req)
    
    @pytest.fixture(scope="class")
    def project_id(self, config):
        """从配置获取默认项目ID"""
        return config.get("project_id", "1947565264656805888")
    
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_approve_list_visit_published(self, approve_api, headers, project_id):
        """测试获取发布访视方案审批列表"""
        with allure.step("获取发布访视方案审批列表"):
            response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type="visit_published",
                page_index=1,
                page_size=20,
                headers=headers
            )
            
        with allure.step("验证响应状态码"):
            assert response.status_code == 200, f"期望状态码200，实际{response.status_code}"
            
        with allure.step("验证响应格式"):
            result = response.json()
            assert "code" in result, "响应中缺少code字段"
            assert "data" in result, "响应中缺少data字段"
            assert "message" in result, "响应中缺少message字段"
            
        with allure.step("记录响应数据"):
            allure.attach(json.dumps(result, ensure_ascii=False, indent=2), 
                         "审批列表响应", allure.attachment_type.JSON)
            
    @allure.severity(allure.severity_level.CRITICAL)
    def test_get_approve_detail(self, approve_api, headers, project_id):
        """测试获取审批详情"""
        with allure.step("先获取审批列表以获取instance_id"):
            list_response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type="visit_published",
                page_index=1,
                page_size=1,
                headers=headers
            )
            
        with allure.step("验证列表响应"):
            assert list_response.status_code == 200
            list_data = list_response.json()
            
        # 如果有待审批记录，则测试详情接口
        records = list_data.get('data', {}).get('records', [])
        if records:
            with allure.step("获取第一个审批的详情"):
                instance_id = records[0].get('instanceId')
                logger.info(f"获取审批详情 - {instance_id}")
                response = approve_api.get_approve_detail(
                    instance_id=instance_id,
                    headers=headers
                )
                
            with allure.step("验证详情响应"):
                assert response.status_code == 200
                detail_data = response.json()
                assert "code" in detail_data
                assert "data" in detail_data
                
            with allure.step("验证详情数据"):
                detail_info = detail_data.get('data', {})
                assert 'instanceId' in detail_info
                assert 'stepId' in detail_info
                assert 'taskUserId' in detail_info
                assert 'routes' in detail_info
                
            with allure.step("验证routes格式"):
                routes = detail_info.get('routes', [])
                assert len(routes) >= 2, "routes应该至少包含通过和驳回两个选项"
                assert 'id' in routes[0], "通过选项应该有id字段"
                assert 'id' in routes[1], "驳回选项应该有id字段"
                
            with allure.step("记录详情数据"):
                allure.attach(json.dumps(detail_data, ensure_ascii=False, indent=2),
                             f"审批详情 - {instance_id}", allure.attachment_type.JSON)
        else:
            with allure.step("跳过详情测试 - 无待审批记录"):
                allure.attach("当前无待审批的发布访视方案", "测试状态", allure.attachment_type.TEXT)
                pytest.skip("当前无待审批的发布访视方案")
    
    @allure.severity(allure.severity_level.CRITICAL)
    def test_submit_approve_pass(self, approve_api, headers, project_id):
        """测试审批通过"""
        with allure.step("获取待审批列表"):
            list_response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type="visit_published",
                page_index=1,
                page_size=1,
                headers=headers
            )
            
        list_data = list_response.json()
        records = list_data.get('data', {}).get('records', [])
        
        if not records:
            with allure.step("跳过审批测试 - 无待审批记录"):
                pytest.skip("当前无待审批的发布访视方案")
                
        with allure.step("获取审批详情"):
            instance_id = records[0].get('instanceId')
            detail_response = approve_api.get_approve_detail(
                instance_id=instance_id,
                headers=headers
            )
            
        detail_data = detail_response.json()
        detail_info = detail_data.get('data', {})
        
        with allure.step("提取审批参数"):
            step_id = detail_info.get('stepId')
            task_user_id = detail_info.get('taskUserId')
            routes = detail_info.get('routes', [])
            
            assert len(routes) >= 1, "需要至少一个路由选项"
            pass_route_id = str(routes[0].get('id', '26'))
            
        with allure.step("提交审批通过"):
            response = approve_api.submit_approve(
                step_id=step_id,
                instance_id=instance_id,
                route_id=pass_route_id,
                task_user_id=task_user_id,
                approved_option_txt="测试审批：发布访视方案已审核通过",
                cc_users=[],
                headers=headers
            )
            
        with allure.step("验证审批结果"):
            assert response.status_code == 200
            result = response.json()
            assert "code" in result
            
            # 记录审批结果
            allure.attach(json.dumps(result, ensure_ascii=False, indent=2),
                         "审批通过结果", allure.attachment_type.JSON)
    
    @allure.severity(allure.severity_level.CRITICAL)
    def test_submit_approve_reject(self, approve_api, headers, project_id):
        """测试审批驳回"""
        with allure.step("获取待审批列表"):
            list_response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type="visit_published",
                page_index=1,
                page_size=1,
                headers=headers
            )
            
        list_data = list_response.json()
        records = list_data.get('data', {}).get('records', [])
        
        if not records:
            with allure.step("跳过审批测试 - 无待审批记录"):
                pytest.skip("当前无待审批的发布访视方案")
                
        with allure.step("获取审批详情"):
            instance_id = records[0].get('instanceId')
            detail_response = approve_api.get_approve_detail(
                instance_id=instance_id,
                headers=headers
            )
            
        detail_data = detail_response.json()
        detail_info = detail_data.get('data', {})
        
        with allure.step("提取审批参数"):
            step_id = detail_info.get('stepId')
            task_user_id = detail_info.get('taskUserId')
            routes = detail_info.get('routes', [])
            
            assert len(routes) >= 2, "需要至少两个路由选项"
            reject_route_id = str(routes[1].get('id', '27'))
            
        with allure.step("提交审批驳回"):
            response = approve_api.submit_approve(
                step_id=step_id,
                instance_id=instance_id,
                route_id=reject_route_id,
                task_user_id=task_user_id,
                approved_option_txt="测试审批：发布访视方案不符合要求，予以驳回",
                cc_users=[],
                headers=headers
            )
            
        with allure.step("验证驳回结果"):
            assert response.status_code == 200
            result = response.json()
            assert "code" in result
            
            # 记录审批结果
            allure.attach(json.dumps(result, ensure_ascii=False, indent=2),
                         "审批驳回结果", allure.attachment_type.JSON)
    
    @allure.severity(allure.severity_level.NORMAL)
    def test_approve_pass_convenience_method(self, approve_api, headers, project_id):
        """测试审批通过快捷方法"""
        with allure.step("获取待审批列表"):
            list_response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type="visit_published",
                page_index=1,
                page_size=1,
                headers=headers
            )
            
        list_data = list_response.json()
        records = list_data.get('data', {}).get('records', [])
        
        if not records:
            pytest.skip("当前无待审批的发布访视方案")
            
        with allure.step("获取审批详情"):
            instance_id = records[0].get('instanceId')
            detail_response = approve_api.get_approve_detail(
                instance_id=instance_id,
                headers=headers
            )
            
        detail_data = detail_response.json()
        detail_info = detail_data.get('data', {})
        
        with allure.step("使用快捷方法审批通过"):
            step_id = detail_info.get('stepId')
            task_user_id = detail_info.get('taskUserId')
            routes = detail_info.get('routes', [])
            
            response = approve_api.approve_pass(
                step_id=step_id,
                instance_id=instance_id,
                task_user_id=task_user_id,
                routes=routes,
                approved_option_txt="快捷审批：发布访视方案通过",
                headers=headers
            )
            
        with allure.step("验证快捷方法结果"):
            assert response.status_code == 200
    
    @allure.severity(allure.severity_level.NORMAL)
    def test_approve_reject_convenience_method(self, approve_api, headers, project_id):
        """测试审批驳回快捷方法"""
        with allure.step("获取待审批列表"):
            list_response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type="visit_published",
                page_index=1,
                page_size=1,
                headers=headers
            )
            
        list_data = list_response.json()
        records = list_data.get('data', {}).get('records', [])
        
        if not records:
            pytest.skip("当前无待审批的发布访视方案")
            
        with allure.step("获取审批详情"):
            instance_id = records[0].get('instanceId')
            detail_response = approve_api.get_approve_detail(
                instance_id=instance_id,
                headers=headers
            )
            
        detail_data = detail_response.json()
        detail_info = detail_data.get('data', {})
        
        with allure.step("使用快捷方法审批驳回"):
            step_id = detail_info.get('stepId')
            task_user_id = detail_info.get('taskUserId')
            routes = detail_info.get('routes', [])
            
            response = approve_api.approve_reject(
                step_id=step_id,
                instance_id=instance_id,
                task_user_id=task_user_id,
                routes=routes,
                approved_option_txt="快捷审批：发布访视方案驳回",
                headers=headers
            )
            
        with allure.step("验证快捷方法结果"):
            assert response.status_code == 200
    
    @allure.severity(allure.severity_level.MINOR)
    def test_get_approve_list_with_pagination(self, approve_api, headers, project_id):
        """测试分页获取审批列表"""
        with allure.step("测试不同分页参数"):
            for page_size in [1, 5, 10]:
                with allure.step(f"测试每页{page_size}条"):
                    response = approve_api.get_approve_list(
                        project_id=project_id,
                        approve_type="visit_published",
                        page_index=1,
                        page_size=page_size,
                        headers=headers
                    )
                    
                    assert response.status_code == 200
                    result = response.json()
                    records = result.get('data', {}).get('records', [])
                    assert len(records) <= page_size
    
    @pytest.mark.parametrize("approve_type", [
        "visit_published",
        "project_milestone_setting_update",
        "project_milestone_date_update",
        "center_milestone_setting_update",
        "center_milestone_date_update",
        "subject_delete",
        "subject_update",
        "change_visit",
        "project_work_hours_visit_crc",
        "project_work_hours_novisit_crc",
        "staff_config_crc",
        "project_close"
    ])
    def test_get_approve_list_all_types(self, approve_api, headers, project_id, approve_type):
        """测试所有审批类型的列表获取"""
        with allure.step(f"测试{approve_type}类型审批列表"):
            response = approve_api.get_approve_list(
                project_id=project_id,
                approve_type=approve_type,
                page_index=1,
                page_size=10,
                headers=headers
            )
            
        with allure.step("验证响应"):
            assert response.status_code == 200
            result = response.json()
            assert "code" in result
            assert "data" in result
            
        with allure.step("记录类型测试结果"):
            records = result.get('data', {}).get('records', [])
            allure.attach(str(len(records)), f"{approve_type}类型记录数")