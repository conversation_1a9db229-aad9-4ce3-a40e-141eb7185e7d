"""
数据库访问工厂类 - 统一创建和管理数据库访问实例
"""
from typing import Optional, Dict, Any
from enum import Enum

from common.core.config_manager import ConfigManager
from common.core.logger import Logger
from common.core.exceptions import ConfigurationError
from common.database.interfaces.i_db_util import IDBUtil
from common.database.interfaces.i_sqlalchemy_util import ISQLAlchemyUtil

logger = Logger().get_logger()

class DBAccessType(Enum):
    """数据库访问类型"""
    PYMYSQL = "pymysql"
    SQLALCHEMY = "sqlalchemy"

class DBFactory:
    """数据库访问工厂类"""
    
    _instances: Dict[str, IDBUtil] = {}
    
    @classmethod
    def create_db_util(cls, access_type: Optional[str] = None, config_name: str = "default") -> IDBUtil:
        """创建数据库访问实例
        
        Args:
            access_type: 访问类型 ("pymysql" 或 "sqlalchemy")
            config_name: 配置名称，用于支持多数据库
            
        Returns:
            IDBUtil: 数据库访问实例
        """
        # 生成实例键
        instance_key = f"{config_name}_{access_type or 'auto'}"
        
        # 检查是否已存在实例
        if instance_key in cls._instances:
            return cls._instances[instance_key]
        
        # 获取配置
        config = ConfigManager()
        db_config = config.get_database_config()
        
        # 确定访问类型
        if access_type is None:
            access_type = db_config.get("access_type", "pymysql")
        
        try:
            access_enum = DBAccessType(access_type.lower())
        except ValueError:
            raise ConfigurationError(
                f"不支持的数据库访问类型: {access_type}",
                config_name="database.access_type",
                supported_types=["pymysql", "sqlalchemy"]
            )
        
        # 创建实例
        if access_enum == DBAccessType.PYMYSQL:
            from .implementations.db_util import DBUtil
            instance = DBUtil()
        elif access_enum == DBAccessType.SQLALCHEMY:
            from .implementations.sqlalchemy_util import SQLAlchemyUtil
            instance = SQLAlchemyUtil()
        else:
            raise ConfigurationError(f"未实现的访问类型: {access_type}")
        
        # 缓存实例
        cls._instances[instance_key] = instance
        
        logger.info(f"创建数据库访问实例: {access_type} ({config_name})")
        return instance
    
    @classmethod
    def create_sqlalchemy_util(cls, config_name: str = "default") -> ISQLAlchemyUtil:
        """创建SQLAlchemy访问实例
        
        Args:
            config_name: 配置名称
            
        Returns:
            ISQLAlchemyUtil: SQLAlchemy访问实例
        """
        instance = cls.create_db_util("sqlalchemy", config_name)
        if not isinstance(instance, ISQLAlchemyUtil):
            raise ConfigurationError("创建的实例不是SQLAlchemy类型")
        return instance
    
    @classmethod
    def get_instance(cls, config_name: str = "default", access_type: Optional[str] = None) -> Optional[IDBUtil]:
        """获取已存在的实例
        
        Args:
            config_name: 配置名称
            access_type: 访问类型
            
        Returns:
            Optional[IDBUtil]: 数据库访问实例，如果不存在则返回None
        """
        instance_key = f"{config_name}_{access_type or 'auto'}"
        return cls._instances.get(instance_key)
    
    @classmethod
    def close_all(cls):
        """关闭所有数据库连接"""
        for instance_key, instance in cls._instances.items():
            try:
                instance.close()
                logger.info(f"关闭数据库连接: {instance_key}")
            except Exception as e:
                logger.error(f"关闭数据库连接失败 {instance_key}: {str(e)}")
        
        cls._instances.clear()
    
    @classmethod
    def health_check_all(cls) -> Dict[str, bool]:
        """检查所有实例的健康状态
        
        Returns:
            Dict[str, bool]: 实例名称到健康状态的映射
        """
        health_status = {}
        for instance_key, instance in cls._instances.items():
            try:
                health_status[instance_key] = instance.health_check()
            except Exception as e:
                logger.error(f"健康检查失败 {instance_key}: {str(e)}")
                health_status[instance_key] = False
        
        return health_status
