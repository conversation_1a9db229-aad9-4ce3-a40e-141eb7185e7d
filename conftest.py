"""
Pytest配置文件 - 优化版本，集成新的配置管理和异常处理
"""
import os
import pytest
import allure
import yaml
import time
from pathlib import Path
from typing import Dict, Any, Generator
from common.core.config_manager import ConfigManager
from common.core.logger import Logger
from common.core.exceptions import AuthenticationError
from common.http.request_util import RequestUtil
from common.database import DBFactory, IDBUtil, ISQLAlchemyUtil
from common.database.implementations.db_util import DBUtil
try:
    from dao.user_dao import UserDAO
except ImportError:
    UserDAO = None

try:
    from dao.project_dao import ProjectDAO
except ImportError:
    ProjectDAO = None

logger = Logger().get_logger()

@pytest.fixture(scope="session")
def config() -> ConfigManager:
    """配置管理器fixture"""
    try:
        config_manager = ConfigManager()
        
        # 验证必需的配置项
        required_keys = ["base_url", "username", "password", "captcha"]
        config_manager.validate_required_config(required_keys)
        
        # 记录安全的配置信息
        safe_config = config_manager.get_safe_config()
        logger.info(f"配置加载成功: {safe_config}")
        
        return config_manager
    except Exception as e:
        logger.error(f"配置初始化失败: {str(e)}")
        pytest.fail(f"配置初始化失败: {str(e)}")

@pytest.fixture(scope="session")
def req(config: ConfigManager) -> Generator[RequestUtil, None, None]:
    """请求工具实例fixture"""
    try:
        api_config = config.get_api_config()
        request_util = RequestUtil(
            base_url=api_config["base_url"],
            timeout=api_config["timeout"],
            max_retries=api_config["max_retries"]
        )
        yield request_util
    finally:
        # 清理资源
        if 'request_util' in locals():
            request_util.close()

@pytest.fixture(scope="session")
def captcha_and_checkkey(req: RequestUtil, config: ConfigManager) -> str:
    """验证码配置处理：根据配置决定是否使用万能验证码"""
    use_universal = config.get("use_universal_captcha", True)
    
    if use_universal:
        logger.info("使用万能验证码模式")
        return "universal_captcha_check_key"
    else:
        logger.info("动态获取验证码checkKey")
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                import time
                url = f"/rpm-api/auth/generateCaptcha?checkKey=&_t={int(time.time() * 1000)}"
                headers = {
                    "accept": "application/json, text/plain, */*",
                    "appid": "app-7zpfoeqntj6",
                    "content-type": "application/json;charset=UTF-8",
                }
                
                logger.info(f"获取验证码checkKey，第{attempt + 1}次尝试")
                response = req.send_request("GET", url, headers=headers)
                
                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get("code") == "200":
                        check_key = response_data["data"]["checkKey"]
                        logger.info(f"验证码checkKey获取成功: {check_key}")
                        return check_key
                    else:
                        logger.warning(f"获取checkKey失败: {response_data.get('message')}")
                else:
                    logger.warning(f"获取checkKey请求失败，状态码: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"获取checkKey异常，第{attempt + 1}次尝试: {str(e)}")
                
            if attempt < max_retries - 1:
                time.sleep(2)  # 重试前等待2秒
        
        error_msg = "获取验证码checkKey失败，达到最大重试次数"
        logger.error(error_msg)
        pytest.fail(error_msg)

@pytest.fixture(scope="session")
def token(req: RequestUtil, config: ConfigManager, captcha_and_checkkey: str) -> str:
    """通过登录接口获取token，带重试机制和详细异常处理"""
    max_retries = 3
    auth_config = config.get_auth_config()
    
    for attempt in range(max_retries):
        try:
            url = "/rpm-api/auth/login"
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json;charset=UTF-8",
                "appid": "app-7zpfoeqntj6",
            }
            data = {
                "username": auth_config["username"],
                "password": auth_config["password"],
                "checkKey": captcha_and_checkkey,
                "captcha": auth_config["captcha"]
            }
            
            logger.info(f"尝试登录，第{attempt + 1}次尝试，用户名: {auth_config['username']}")
            response = req.send_request("POST", url, json=data, headers=headers)
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "200":
                    token_value = response_data["data"]["token"]
                    logger.info("登录成功，获取到token")
                    return token_value
                else:
                    error_message = response_data.get("message", "登录失败")
                    logger.warning(f"登录失败: {error_message}")
                    
                    # 特定错误处理
                    if "验证码" in error_message:
                        logger.warning("验证码错误，可能需要更新captcha配置")
                    elif "用户名" in error_message or "密码" in error_message:
                        raise AuthenticationError(
                            f"用户名或密码错误: {error_message}",
                            username=auth_config["username"],
                            response_code=response.status_code
                        )
            else:
                logger.warning(f"登录请求失败，状态码: {response.status_code}")
                
        except AuthenticationError:
            # 认证错误不重试
            raise
        except Exception as e:
            import traceback
            logger.error(f"登录异常详情: {str(e)}")
            logger.error(f"异常类型: {type(e)}")
            logger.error(f"堆栈信息: {traceback.format_exc()}")
            logger.warning(f"登录异常，第{attempt + 1}次尝试: {str(e)}")
            
        if attempt < max_retries - 1:
            time.sleep(2)  # 重试前等待2秒
    
    error_msg = "登录失败，达到最大重试次数"
    logger.error(error_msg)
    raise AuthenticationError(error_msg, username=auth_config["username"])

@pytest.fixture(scope="session")
def headers(token: str) -> Dict[str, str]:
    """自动生成带token的请求头"""
    headers_dict = {"Authorization": f"Bearer {token}","Appid": "app-7zpfoeqntj6"}
    logger.info("生成请求头成功")
    return headers_dict

# 删除合同相关的fixture
# @pytest.fixture(scope="session")
# def contract_data() -> Dict[str, Any]:
#     """加载合同测试数据"""
#     data_file = Path(__file__).parent / "data" / "contract_data.yaml"
#     
#     try:
#         logger.info(f"加载测试数据文件: {data_file}")
#         with open(data_file, encoding="utf-8") as f:
#             data = yaml.safe_load(f)
#             if not data:
#                 raise ValueError("测试数据文件为空")
#             return data
#     except FileNotFoundError:
#         error_msg = f"测试数据文件不存在: {data_file}"
#         logger.error(error_msg)
#         pytest.fail(error_msg)
#     except Exception as e:
#         error_msg = f"加载测试数据失败: {str(e)}"
#         logger.error(error_msg)
#         pytest.fail(error_msg)

@pytest.fixture(scope="session")
def subject_data() -> Dict[str, Any]:
    """加载受试者测试数据"""
    data_file = Path(__file__).parent / "data" / "subject_data.yaml"

    try:
        logger.info(f"加载受试者测试数据文件: {data_file}")
        with open(data_file, encoding="utf-8") as f:
            data = yaml.safe_load(f)
            if not data:
                raise ValueError("受试者测试数据文件为空")
            return data
    except FileNotFoundError:
        error_msg = f"受试者测试数据文件不存在: {data_file}"
        logger.error(error_msg)
        # 返回默认数据而不是失败
        return {
            "project_config": {
                "default_project_id": "1930148914887311362"
            },
            "create_subject": {
                "genders": ["female", "male"]
            }
        }
    except Exception as e:
        error_msg = f"加载受试者测试数据失败: {str(e)}"
        logger.error(error_msg)
        pytest.fail(error_msg)


@pytest.fixture(scope="session")
def project_hour_data() -> Dict[str, Any]:
    """加载项目工时测试数据"""
    data_file = Path(__file__).parent / "data" / "project_hour_data.yaml"

    try:
        logger.info(f"加载项目工时测试数据文件: {data_file}")
        with open(data_file, encoding="utf-8") as f:
            data = yaml.safe_load(f)
            if not data:
                raise ValueError("项目工时测试数据文件为空")
            return data
    except FileNotFoundError:
        error_msg = f"项目工时测试数据文件不存在: {data_file}"
        logger.error(error_msg)
        # 返回默认数据而不是失败
        return {
            "project_config": {
                "default_project_id": "1945711786514358274",#项目编号
                "test_projectSimpleName": "cstqlx_0717" # 项目简称
            }
        }
    except Exception as e:
        error_msg = f"项目工时测试数据加载失败: {str(e)}"
        logger.error(error_msg)
        pytest.fail(error_msg)



@pytest.fixture(scope="session")
def db_util_pymysql() -> Generator[IDBUtil, None, None]:
    """PyMySQL数据库访问fixture"""
    db_util = DBFactory.create_db_util("pymysql")
    yield db_util
    db_util.close()

@pytest.fixture(scope="session")
def db_util_sqlalchemy() -> Generator[ISQLAlchemyUtil, None, None]:
    """SQLAlchemy数据库访问fixture - 按需加载"""
    db_util = DBFactory.create_sqlalchemy_util()
    yield db_util
    db_util.close()

@pytest.fixture(scope="session")
def db_util() -> Generator[IDBUtil, None, None]:
    """默认数据库访问fixture（PyMySQL）"""
    db_util = DBFactory.create_db_util("pymysql")  # 明确指定使用PyMySQL
    yield db_util
    db_util.close()

@pytest.fixture(scope="function")
def db_session(db_util_sqlalchemy):
    """SQLAlchemy会话fixture（函数级别，每个测试独立）"""
    with db_util_sqlalchemy.session_scope() as session:
        yield session

@pytest.fixture(scope="session", autouse=False)  # 改为非自动执行
def setup_database(db_util_sqlalchemy):
    """设置测试数据库（可选）"""
    try:
        # 创建所有表
        db_util_sqlalchemy.create_all()
        yield
    finally:
        # 清理可以在这里进行，或者保留表结构
        pass

def pytest_sessionfinish(session, exitstatus):
    """测试会话结束时的清理"""
    DBFactory.close_all()

@pytest.fixture(scope="function")
def user_dao():
    """用户DAO fixture"""
    if UserDAO is None:
        pytest.skip("UserDAO未正确导入")
    return UserDAO()()

@pytest.fixture(scope="function")
def project_dao():
    """项目DAO fixture"""
    if ProjectDAO is None:
        pytest.skip("ProjectDAO未正确导入")
    return ProjectDAO()()

@pytest.fixture(scope="function")
def test_data_cleaner(db_util: IDBUtil):
    """测试数据清理器fixture"""
    created_records = {
        'users': [],
        'projects': []
    }
    
    yield created_records
    
    # 测试结束后清理数据
    try:
        # 清理用户数据
        for user_id in created_records['users']:
            db_util.execute_update(
                "UPDATE sys_user SET deleted = 1 WHERE id = %s", 
                (user_id,)
            )
        
        # 清理项目数据
        for project_id in created_records['projects']:
            db_util.execute_update(
                "UPDATE project SET deleted = 1 WHERE id = %s", 
                (project_id,)
            )
        
        logger.info(f"清理测试数据完成: 用户{len(created_records['users'])}个, 项目{len(created_records['projects'])}个")
    
    except Exception as e:
        logger.error(f"清理测试数据失败: {str(e)}")

# pytest钩子函数
def pytest_configure(config):
    """pytest配置钩子"""
    logger.info("=" * 80)
    logger.info("开始执行RPM自动化测试")
    logger.info("=" * 80)
    
    # 添加自定义标记
    config.addinivalue_line("markers", "critical: 标记为关键测试用例")
    config.addinivalue_line("markers", "smoke: 标记为冒烟测试用例")
    config.addinivalue_line("markers", "regression: 标记为回归测试用例")

def pytest_unconfigure(config):
    """pytest结束钩子"""
    logger.info("=" * 80)
    logger.info("RPM自动化测试执行完成")
    logger.info("=" * 80)
    
    # 测试完成后发送钉钉通知
    try:
        from common.integrations.dingtalk_notifier import DingTalkNotifier
        from common.core.config_manager import ConfigManager
        import os
        import datetime
        
        config_manager = ConfigManager()
        
        # 检查是否启用钉钉通知
        if not config_manager.get("dingtalk_enabled", True):
            logger.info("钉钉通知已禁用")
            return
            
        if not config_manager.get("dingtalk_webhook"):
            logger.info("钉钉webhook未配置，跳过通知")
            return
        
        notifier = DingTalkNotifier(config_manager)
        
        # 获取Allure结果目录 - 支持多种可能的路径
        possible_dirs = [
            config_manager.get("allure_results_dir", "./allure-results"),
            "./allure-results",
            "./allure-results-stress",  # 压测环境特殊目录
            f"./allure-results-{os.getenv('TEST_ENV', 'default')}"  # 环境特定目录
        ]
        
        allure_results_dir = None
        from pathlib import Path
        
        # 查找存在的结果目录
        for dir_path in possible_dirs:
            if Path(dir_path).exists():
                result_files = list(Path(dir_path).glob("*-result.json"))
                if result_files:  # 确保目录中有结果文件
                    allure_results_dir = dir_path
                    logger.info(f"找到Allure结果目录: {allure_results_dir} (包含{len(result_files)}个结果文件)")
                    break
        
        if not allure_results_dir:
            logger.warning(f"未找到有效的Allure结果目录，已检查: {possible_dirs}")
            logger.warning("请确保运行pytest时使用了 --alluredir 参数")
            
            # 即使没有结果文件，也发送一个简单的通知
            simple_message = f"""## 🚨 RPM自动化测试完成

> **执行时间:** {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
> **执行环境:** {os.getenv('TEST_ENV', 'default')}  
> **状态:** ⚠️ 测试完成但未找到详细结果

**注意**: 未找到Allure测试结果文件，请检查pytest执行参数。

**已检查的目录**:
{chr(10).join([f"- `{d}`" for d in possible_dirs])}

---
💡 建议使用: `pytest --alluredir=./allure-results`
"""
            
            success = notifier.send_markdown_message(
                title="RPM自动化测试完成 - 缺少结果文件",
                content=simple_message,
                at_all=False
            )
            
            if success:
                logger.info("✅ 简化版钉钉通知发送成功")
            else:
                logger.warning("❌ 简化版钉钉通知发送失败")
            return
        
        # 发送测试报告
        logger.info("开始发送钉钉测试报告...")
        success = notifier.send_test_report(allure_results_dir)
        if success:
            logger.info("✅ 钉钉测试报告发送成功")
        else:
            logger.warning("❌ 钉钉测试报告发送失败")
            
    except ImportError as e:
        logger.warning(f"钉钉通知模块导入失败: {str(e)}")
    except Exception as e:
        logger.error(f"发送钉钉通知异常: {str(e)}")
        # 不影响测试结果，只记录错误

def pytest_runtest_setup(item):
    """每个测试用例开始前的钩子"""
    logger.info(f"开始执行测试用例: {item.name}")

def pytest_runtest_teardown(item, nextitem):
    """每个测试用例结束后的钩子"""
    logger.info(f"测试用例执行完成: {item.name}")

def pytest_runtest_makereport(item, call):
    """生成测试报告钩子"""
    if call.when == "call":
        if call.excinfo is not None:
            # 测试失败时记录详细信息
            logger.error(f"测试用例失败: {item.name}")
            logger.error(f"失败原因: {call.excinfo.value}")

@pytest.fixture(autouse=True)
def test_environment_check(config: ConfigManager):
    """自动检查测试环境"""
    try:
        # 检查配置完整性
        api_config = config.get_api_config()
        if not api_config.get("base_url"):
            pytest.fail("base_url配置缺失")
        
        # 记录测试环境信息
        logger.info(f"测试环境: {api_config['base_url']}")
        
    except Exception as e:
        logger.error(f"环境检查失败: {str(e)}")
        pytest.fail(f"环境检查失败: {str(e)}")
