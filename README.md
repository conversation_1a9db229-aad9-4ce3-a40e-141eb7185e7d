# RPM自动化测试框架

基于 `pytest + requests + allure` 的接口自动化测试框架，支持登录认证、验证码处理、数据驱动、日志记录、报告生成等功能。

## 🚀 功能特性

- ✅ **自动登录**: 支持验证码自动获取和token管理，带重试机制
- ✅ **业务封装**: ProjectAPI等业务化封装，测试用例更清晰
- ✅ **数据库访问**: PyMySQL + SQLAlchemy双重支持，接口与实现分离的灵活数据库访问层
- ✅ **数据驱动**: YAML配置测试数据，支持参数化测试
- ✅ **日志记录**: 详细的请求响应日志，包含请求头、入参、响应内容，自动脱敏敏感信息
- ✅ **报告生成**: 精美的中文Allure测试报告，包含步骤、附件、趋势分析
- ✅ **重试机制**: 网络异常、服务器错误自动重试，提升稳定性
- ✅ **断言工具**: 丰富的响应断言方法（AssertUtil）
- ✅ **环境管理**: 支持多环境配置切换，环境变量覆盖
- ✅ **异常处理**: 完善的错误处理和超时控制
- ✅ **安全防护**: 敏感信息自动脱敏，配置安全验证
- ✅ **配置管理**: 统一配置管理器，支持环境变量覆盖
- ✅ **精确异常**: 细化异常类型，提供详细错误上下文
- ✅ **钉钉通知**: 测试完成后自动发送报告到钉钉群，支持多环境配置
- ✅ **数据库集成**: 完整的DAO层和模型层，支持复杂业务逻辑
- ✅ **工厂模式**: 统一的数据库访问工厂，支持动态切换访问方式
- ✅ **模块化架构**: 核心基础设施、数据库、HTTP、API分层设计，企业级架构标准
- ✅ **接口分离**: 数据库访问层接口与实现分离，支持灵活扩展和测试

## 📁 项目结构

```
rpm_auto/
├── common/                 # 公共模块（模块化架构）
│   ├── core/              # 核心基础设施模块
│   │   ├── __init__.py   # 核心组件统一导出
│   │   ├── config_manager.py # 配置管理器（统一配置管理）
│   │   ├── logger.py     # 日志管理工具
│   │   ├── exceptions.py # 自定义异常类（精确错误处理）
│   │   └── security_util.py # 安全工具类（敏感信息脱敏）
│   ├── database/         # 数据库访问层
│   │   ├── __init__.py   # 数据库模块导出
│   │   ├── db_factory.py # 数据库工厂（统一创建和管理）
│   │   ├── interfaces/   # 数据库接口定义
│   │   │   ├── i_db_util.py # PyMySQL接口
│   │   │   └── i_sqlalchemy_util.py # SQLAlchemy接口
│   │   └── implementations/ # 数据库实现层
│   │       ├── db_util.py # PyMySQL实现
│   │       └── sqlalchemy_util.py # SQLAlchemy实现
│   ├── http/             # HTTP请求处理模块
│   │   ├── __init__.py   # HTTP模块导出
│   │   ├── request_util.py # HTTP请求工具类（带重试、日志、脱敏）
│   │   └── assert_util.py # 响应断言工具类
│   └── apis/             # API业务封装层
│       ├── __init__.py   # API模块导出
│       ├── project_api.py # 项目管理API封装
│       ├── visitingplan_api.py # 访视方案API封装
│       └── subject_api.py # 受试者管理API封装
├── dao/                   # 数据访问对象层
│   ├── __init__.py       # DAO模块统一导出
│   ├── base_dao.py       # 基础DAO抽象类
│   ├── user_dao.py       # 用户管理DAO（双实现）
│   └── project_dao.py    # 项目管理DAO（双实现）
├── models/                # 数据模型层
│   ├── base_model.py     # 基础模型类
│   ├── user.py           # 用户模型
│   └── project.py        # 项目模型
├── config/                # 配置文件
│   ├── config.yaml       # 主配置文件
│   ├── config.yaml.example # 配置文件示例
│   ├── config_dev.yaml   # 开发环境配置（可选）
│   ├── config_uat.yaml   # UAT环境配置（可选）
│   ├── config_stress.yaml # 压测环境配置（可选）
│   └── config_prod.yaml  # 生产环境配置（可选）
├── data/                  # 测试数据
│   └── subject_data.yaml # 受试者管理测试数据
├── docs/                  # 文档目录
│   ├── DATABASE_GUIDE.md # 数据库访问层使用指南
│   └── DINGTALK_SETUP.md # 钉钉配置指南
├── testcases/             # 测试用例
│   ├── test_project.py   # 项目管理测试
│   ├── test_visitingplan.py # 访视方案测试
│   ├── test_subject.py   # 受试者管理测试
│   ├── test_database_integration.py # 数据库集成测试
│   └── test_user_with_db.py # 用户API与数据库集成测试
├── examples/              # 使用示例
│   └── database_usage_examples.py # 数据库使用示例
├── logs/                  # 日志文件目录（自动生成）
├── allure-results/        # Allure报告原始数据
├── conftest.py           # pytest配置和fixture
├── send_dingtalk_report.py # 钉钉报告发送脚本
├── requirements.txt      # 依赖包列表
├── .gitignore           # Git忽略文件
└── README.md            # 项目说明文档
```

## 🛠 环境要求

- Python 3.7+
- pip
- Java 8+ (运行Allure报告需要)

## 📦 安装和配置

### 1. 克隆项目并安装依赖

```bash
# 克隆项目
git clone <your-repo-url>
cd rpm_auto

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 安装Allure命令行工具

#### 方法一：通过Homebrew（推荐，如果可以访问外网）
```bash
brew install allure
```

#### 方法二：手动安装
1. 下载 [allure-commandline-2.13.2.tgz](https://github.com/allure-framework/allure2/releases)
2. 解压并配置环境变量：
```bash
tar -xzf allure-commandline-2.13.2.tgz
mv allure-2.13.2 ~/allure
echo 'export PATH=$PATH:~/allure/bin' >> ~/.zshrc
source ~/.zshrc
```

3. 验证安装：
```bash
allure --version
```

## ⚙️ 配置说明

### 基础配置

复制 `config/config.yaml.example` 为 `config/config.yaml` 并编辑：

```yaml
# 基础配置
base_url: "https://rpm-uat.pharmaronclinical.com"
username: "your_username"
password: "your_password"
captcha: "your_captcha"  # 万能验证码，请联系开发团队获取

# 请求配置
timeout: 30              # 请求超时时间（秒）
max_retries: 3           # 最大重试次数
verify_ssl: true         # 是否验证SSL证书

# 日志配置
log_level: "INFO"        # 日志级别: DEBUG, INFO, WARNING, ERROR
log_max_size: "10MB"     # 日志文件最大大小
log_backup_count: 5      # 日志文件备份数量

# Allure报告配置
allure_results_dir: "./allure-results"
allure_report_dir: "./allure-report"
allure_clean_results: true

# 钉钉通知配置
dingtalk_webhook: ""     # 钉钉机器人webhook地址，留空则不发送通知
dingtalk_secret: ""      # 钉钉机器人加签密钥（可选）
dingtalk_enabled: true   # 是否启用钉钉通知
dingtalk_at_all: false   # 是否默认@所有人
dingtalk_at_mobiles: []  # 需要@的手机号列表
```

### 钉钉通知配置

框架支持测试完成后自动发送报告到钉钉群，详细配置请参考 [钉钉配置指南](docs/DINGTALK_SETUP.md)。

#### 快速配置钉钉通知

1. **创建钉钉机器人**：在钉钉群中添加自定义机器人
2. **获取webhook地址**：复制机器人的webhook URL
3. **配置文件设置**：
```yaml
dingtalk_webhook: "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"
dingtalk_enabled: true
```

#### 多环境钉钉配置

支持为不同环境配置不同的钉钉群：

```bash
# 开发环境配置
config/config_dev.yaml
dingtalk_enabled: false  # 开发环境不发送通知

# UAT环境配置  
config/config_uat.yaml
dingtalk_webhook: "UAT环境钉钉群webhook"
dingtalk_at_mobiles: ["测试负责人手机号"]

# 压测环境配置
config/config_stress.yaml
dingtalk_webhook: "压测环境钉钉群webhook"
dingtalk_at_mobiles: ["压测负责人手机号"]

# 生产环境配置
config/config_prod.yaml
dingtalk_webhook: "生产环境钉钉群webhook"
dingtalk_at_all: true  # 生产环境失败时@所有人
```

### 环境变量支持

可通过环境变量覆盖配置：

```bash
export BASE_URL="https://your-api-url.com"
export USERNAME="your_username"
export PASSWORD="your_password"
export TEST_ENV="prod"  # 可指定配置文件后缀
export TIMEOUT="60"
export MAX_RETRIES="5"

# 钉钉相关环境变量
export DINGTALK_WEBHOOK="https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"
export DINGTALK_ENABLED="true"
export DINGTALK_AT_ALL="false"
```

### 多环境配置

支持环境特定配置文件：

```bash
# 开发环境
config/config_dev.yaml

# UAT环境
config/config_uat.yaml

# 压测环境
config/config_stress.yaml

# 生产环境
config/config_prod.yaml
```

使用方式：
```bash
# 指定环境
export TEST_ENV="uat"
pytest --alluredir=./allure-results

# 压测环境
export TEST_ENV="stress"
pytest --alluredir=./allure-results-stress

# 或者运行时指定
TEST_ENV=prod pytest --alluredir=./allure-results
```

### 验证码配置 

本框架支持自动获取验证码checkKey，但需要正确的验证码才能登录成功：

1. **万能验证码**：测试环境通常提供万能验证码，请联系开发团队获取
2. **验证码错误**：如果看到 `"code":"0019","message":"验证码错误"`，说明配置的验证码不正确
3. **自动识别**：可集成打码平台实现验证码自动识别（见开发指南）

## 🏃 运行测试

### 运行所有测试

```bash
pytest --alluredir=./allure-results
```

### 运行指定模块

```bash
# 运行项目管理测试
pytest testcases/test_project_hour.py --alluredir=./allure-results

# 运行访视方案测试
pytest testcases/test_visitingplan.py --alluredir=./allure-results

# 运行受试者管理测试
pytest testcases/test_subject.py --alluredir=./allure-results
```

### 强制指定用例

```bash
# 运行合同数据迁移测试
pytest testcases/test_project.py::TestProject::test_project_data_migration -v

# 运行项目卡片列表测试
pytest testcases/test_project.py::TestProject::test_get_project_card_list -v

# 使用标记运行
pytest -m "critical" --alluredir=./allure-results
```

### Allure报告特性

- **中文支持**：测试用例、步骤、错误信息均为中文
- **详细步骤**：每个测试操作都有清晰的步骤记录
- **附件信息**：自动附加请求响应详情
- **趋势分析**：支持历史测试结果对比
- **严重级别**：用例按重要性分级（CRITICAL、NORMAL等）

### 钉钉通知特性

- **自动发送**：测试完成后自动发送报告到钉钉群
- **多环境支持**：不同环境发送到不同钉钉群
- **智能@人**：根据测试结果和环境配置智能决定@人策略
- **丰富信息**：包含测试统计、成功率、执行时长、环境信息等
- **安全可靠**：支持加签验证，详细的错误处理和重试机制

#### 钉钉通知规则

- **成功率 ≥ 95%**：✅ 优秀状态，绿色显示
- **成功率 ≥ 80%**：⚠️ 良好状态，橙色显示  
- **成功率 < 80%**：❌ 需要关注，红色显示

#### 自动@人策略

- **开发环境**：默认不发送通知
- **UAT环境**：失败数 > 5 或成功率 < 80% 时@指定人员
- **压测环境**：发送通知，@压测负责人
- **生产环境**：根据配置@所有人或关键人员

## 🔧 开发指南

### 模块化架构设计

框架采用分层模块化架构，各模块职责清晰：

#### **核心基础设施层 (`common/core/`)**
- **配置管理器**：统一配置管理，支持多环境和环境变量覆盖
- **日志管理**：结构化日志记录，自动脱敏敏感信息
- **异常体系**：精确的异常分类，提供详细错误上下文
- **安全工具**：敏感信息脱敏，配置安全验证

#### **数据库访问层 (`common/database/`)**
- **接口定义**：抽象数据库操作接口，支持多种实现
- **具体实现**：PyMySQL和SQLAlchemy双重实现
- **工厂模式**：统一创建和管理数据库连接

#### **HTTP处理层 (`common/http/`)**
- **请求工具**：带重试、日志、脱敏的HTTP客户端
- **断言工具**：丰富的响应验证方法

#### **API封装层 (`common/apis/`)**
- **业务封装**：面向业务的API调用封装
- **统一接口**：标准化的API调用方式

### 配置管理使用

框架提供了统一的配置管理器，支持多环境和环境变量覆盖：

```python
from common.core.config_manager import ConfigManager

# 获取配置管理器实例（单例模式）
config = ConfigManager()

# 获取API相关配置
api_config = config.get_api_config()
# 返回: {"base_url": "...", "timeout": 30, "max_retries": 3, ...}

# 获取认证相关配置
auth_config = config.get_auth_config()
# 返回: {"username": "...", "password": "...", "captcha": "..."}

# 获取日志相关配置
log_config = config.get_logging_config()

# 获取Allure相关配置
allure_config = config.get_allure_config()

# 获取单个配置项（支持嵌套键）
base_url = config.get("base_url")
db_host = config.get("database.host", "localhost")  # 支持默认值

# 设置配置项
config.set("custom.setting", "value")

# 验证必需配置
config.validate_required_config(["base_url", "username", "password"])

# 获取脱敏后的配置（用于日志记录）
safe_config = config.get_safe_config()
```

### 安全性和敏感信息处理

框架提供了全面的敏感信息脱敏功能：

```python
from common.core.security_util import SecurityUtil

# 自动脱敏任意数据结构
original_data = {
    "username": "testuser",
    "password": "secret123",
    "token": "abc123def456",
    "user_info": {
        "name": "张三",
        "auth_key": "sensitive_key"
    }
}

safe_data = SecurityUtil.sanitize_data(original_data)
# 结果: {
#     "username": "testuser",
#     "password": "s***3",
#     "token": "ab***56",
#     "user_info": {
#         "name": "张三",
#         "auth_key": "se***ey"
#     }
# }

# 脱敏字符串中的敏感信息
log_message = "用户登录: username=admin&password=123456&token=abc123"
safe_message = SecurityUtil.sanitize_data(log_message)
# 结果: "用户登录: username=admin&password=***&token=***"

# 验证配置安全性
security_issues = SecurityUtil.validate_config_security(config_dict)
if security_issues:
    for issue in security_issues:
        logger.warning(f"安全警告: {issue}")
```

### 异常处理和错误管理

框架提供了详细的异常分类，便于精确处理不同类型的错误：

```python
from common.core.exceptions import (
    APIRequestError, AuthenticationError, NetworkError, 
    ValidationError, ConfigurationError, TimeoutError
)
from common.http.request_util import RequestUtil

try:
    req = RequestUtil(base_url="https://api.example.com")
    response = req.send_request("POST", "/api/login", json=login_data)
    
except AuthenticationError as e:
    # 认证失败
    logger.error(f"认证失败: {e.message}")
    logger.error(f"用户名: {e.username}, 响应码: {e.response_code}")
    
except NetworkError as e:
    # 网络连接问题
    logger.error(f"网络错误: {e.message}")
    logger.error(f"URL: {e.url}, 重试次数: {e.retry_count}")
    
except TimeoutError as e:
    # 请求超时
    logger.error(f"请求超时: {e.message}")
    logger.error(f"超时时间: {e.timeout_seconds}秒, 操作: {e.operation}")
    
except APIRequestError as e:
    # API请求错误
    logger.error(f"API错误: {e.message}")
    logger.error(f"方法: {e.method}, URL: {e.url}, 状态码: {e.status_code}")
    
except ValidationError as e:
    # 数据验证错误
    logger.error(f"验证失败: {e.message}")
    logger.error(f"字段: {e.field_name}, 期望: {e.expected_value}, 实际: {e.actual_value}")
    
except ConfigurationError as e:
    # 配置错误
    logger.error(f"配置错误: {e.message}")
    logger.error(f"配置项: {e.config_key}")
```

### HTTP请求和响应处理

框架提供了强大的HTTP请求工具和响应断言功能：

```python
from common.http.request_util import RequestUtil
from common.http.assert_util import AssertUtil

# 创建请求工具实例
req = RequestUtil(base_url="https://api.example.com")

# 发送请求
response = req.send_request("POST", "/api/users", json=user_data)

# 响应断言
AssertUtil.assert_response_success(response)
AssertUtil.assert_response_field_value(response, "data.name", "期望的用户名")
AssertUtil.assert_field_type(response, "data.id", int)
```

### 添加新的API封装

1. 在 `common/apis/` 目录下创建新的API封装类（如 `user_api.py`）：
```python
import allure
from common.core.logger import Logger
from common.core.config_manager import ConfigManager

logger = Logger().get_logger()

class UserAPI:
    def __init__(self, request_util):
        self.req = request_util
        self.config = ConfigManager()
    
    @allure.step("创建用户")
    def create_user(self, payload, headers):
        url = "/rpm-api/user/create"
        logger.info(f"创建用户请求: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"创建用户响应: {response.status_code}")
        return response
```

### 数据库访问使用

框架提供了灵活的数据库访问层，支持两种访问方式：

```python
from common.database import DBFactory

# 使用PyMySQL（轻量级，高性能）
db_util = DBFactory.create_db_util("pymysql")
users = db_util.execute_query("SELECT * FROM sys_user WHERE status = %s", ('active',))

# 使用SQLAlchemy（功能完整的ORM）
db_util = DBFactory.create_sqlalchemy_util()
with db_util.session_scope() as session:
    user = User(username='test', email='<EMAIL>')
    session.add(user)

# 使用DAO层（推荐）
from dao.user_dao import UserDAO
user_dao = UserDAO()
user = user_dao.create_user(username="test", email="<EMAIL>")
```

详细使用指南请参考 [数据库访问层使用指南](docs/DATABASE_GUIDE.md)。

### 添加新的测试用例

1. 在 `testcases/` 目录下创建测试文件（如 `test_user.py`）：
```python
import allure
import pytest
from common.apis.user_api import UserAPI
from common.http.assert_util import AssertUtil
from common.core.exceptions import APIRequestError, ValidationError

@allure.feature("用户管理")
class TestUser:
    
    @allure.story("用户创建")
    @allure.severity(allure.severity_level.CRITICAL)
    def test_create_user(self, req, token):
        """测试创建用户功能"""
        user_api = UserAPI(req)
        headers = {"Authorization": f"Bearer {token}"}
        
        payload = {
            "username": "test_user",
            "email": "<EMAIL>",
            "role": "user"
        }
        
        with allure.step("发送创建用户请求"):
            response = user_api.create_user(payload, headers)
        
        with allure.step("验证响应结果"):
            AssertUtil.assert_response_success(response)
            AssertUtil.assert_response_field_value(response, "data.username", "test_user")
```

## 🚨 常见问题

### 1. 登录失败："验证码错误"
- **原因**：config.yaml中的captcha配置错误
- **解决**：联系开发团队获取正确的万能验证码

### 2. 配置加载失败
- **原因**：配置文件格式错误或缺少必需配置项
- **解决**：检查YAML格式，确保必需配置项存在
- **日志**：查看详细的配置加载日志

### 3. 网络超时
- **原因**：网络不稳定或服务器响应慢
- **解决**：框架已内置重试机制，可调整timeout和max_retries配置

### 4. 敏感信息泄露
- **原因**：日志中包含明文密码或token
- **解决**：框架自动脱敏，如有遗漏请检查SecurityUtil配置

### 5. Allure报告打不开
- **原因**：Allure命令行工具未正确安装
- **解决**：按照安装指南重新安装Allure

### 6. 找不到模块错误
- **原因**：依赖包未安装或虚拟环境问题
- **解决**：`pip install -r requirements.txt`

### 7. 钉钉通知发送失败
- **原因**：webhook配置错误、网络问题或机器人被禁用
- **解决**：
  - 检查webhook地址是否正确
  - 确认机器人安全设置（关键词、IP白名单、加签）
  - 查看日志获取详细错误信息
  - 使用 `--dry-run` 参数预览报告内容

### 8. 环境配置不生效
- **原因**：环境变量设置错误或配置文件路径问题
- **解决**：
  - 确认 `TEST_ENV` 环境变量设置正确
  - 检查环境特定配置文件是否存在
  - 验证配置优先级：环境变量 > 环境配置 > 主配置 > 默认值

## 📋 注意事项

1. **敏感信息**：请勿在代码中硬编码密码、token等敏感信息
2. **环境隔离**：不同环境使用不同的配置文件
3. **数据清理**：测试完成后及时清理测试数据
4. **并发测试**：注意并发测试时的数据冲突问题
5. **验证码有效期**：验证码checkKey有时效性，框架会自动重新获取
6. **配置安全**：定期检查配置文件安全性，避免敏感信息泄露
7. **异常处理**：使用框架提供的异常类型进行精确错误处理
8. **钉钉配置**：
   - 不同环境配置不同的钉钉群，避免消息发错群
   - 生产环境建议启用加签验证，提高安全性
   - 合理设置@人策略，避免过度打扰
   - 定期检查机器人状态，确保通知正常

## 🔒 安全特性

### 敏感信息保护
- ✅ 自动识别和脱敏敏感字段（password、token、key等）
- ✅ 支持多种数据格式的脱敏处理
- ✅ 配置文件安全性验证
- ✅ 日志输出自动脱敏

### 配置安全
- ✅ 环境变量覆盖机制，避免硬编码
- ✅ 配置文件示例模板
- ✅ 安全警告和检查机制

### 请求安全
- ✅ 请求追踪ID，便于问题排查
- ✅ 详细的异常分类和处理
- ✅ 自动重试和超时控制

### 钉钉通知安全
- ✅ 支持加签验证，防止恶意请求
- ✅ webhook地址脱敏显示
- ✅ 多环境隔离，避免消息发错群
- ✅ 详细的错误处理和日志记录

## 🏆 项目亮点

- **企业级架构**：模块化设计，核心基础设施、数据库、HTTP、API分层清晰
- **接口分离**：数据库访问层接口与实现分离，支持灵活扩展和单元测试
- **安全性**：全面的敏感信息保护和配置安全验证
- **可维护**：清晰的代码结构和业务封装，符合SOLID原则
- **可扩展**：模块化架构便于添加新功能和集成第三方服务
- **实用性**：解决了验证码、重试、环境切换等实际问题
- **专业性**：符合自动化测试最佳实践和企业开发规范
- **稳定性**：详细的异常处理和重试机制，提供精确的错误上下文
- **可观测**：完善的日志记录和请求追踪，支持问题快速定位
- **智能通知**：多环境钉钉通知，智能@人策略
- **多环境支持**：完整的多环境配置和管理体系
- **工厂模式**：统一的数据库访问工厂，支持动态切换访问方式

## 📈 最新更新

### v2.1.0 - 钉钉通知和多环境增强
- ✅ 新增钉钉通知功能，测试完成自动推送报告
- ✅ 完善多环境配置支持，不同环境独立配置
- ✅ 智能@人策略，根据环境和测试结果自动决策
- ✅ 支持加签验证，提升钉钉通知安全性
- ✅ 新增手动发送钉钉报告脚本
- ✅ 优化配置管理器，支持环境变量覆盖钉钉配置
- ✅ 完善错误处理，提供详细的钉钉API错误信息
- ✅ 新增钉钉配置指南文档

### v2.0.0 - 企业级安全和配置管理
- ✅ 新增统一配置管理器（ConfigManager）
- ✅ 新增敏感信息脱敏工具（SecurityUtil）
- ✅ 新增详细异常分类体系
- ✅ 优化请求工具类，增强异常处理
- ✅ 改进conftest.py，集成新的配置和异常处理
- ✅ 增强安全性，自动脱敏敏感信息
- ✅ 支持多环境配置和环境变量覆盖
- ✅ 提供请求追踪ID，便于问题排查

### 升级指南
1. 复制新的配置文件示例：`cp config/config.yaml.example config/config.yaml`
2. 更新配置文件格式（参考上面的配置说明）
3. 配置钉钉通知（可选）：参考 [钉钉配置指南](docs/DINGTALK_SETUP.md)
4. 安装新的依赖：`pip install -r requirements.txt`
5. 运行测试验证升级：`pytest --alluredir=./allure-results`

### 日志记录特性

框架提供了完整的请求日志记录功能：

- **请求追踪**：每个请求都有唯一的追踪ID，便于问题排查
- **详细信息**：记录请求方法、URL、请求头、请求参数、响应状态、响应时间等
- **请求体记录**：支持JSON、表单数据等多种请求体格式的记录
- **查询参数**：记录URL查询参数
- **响应信息**：记录响应头、响应内容（限制长度）
- **自动脱敏**：敏感信息（密码、token等）自动脱敏处理
- **重试日志**：记录重试过程和等待时间
- **异常日志**：详细记录各种异常情况

#### 日志示例

```
2024-01-15 10:30:25 - request_util - INFO - [a1b2c3d4] 发送请求 - POST https://api.example.com/login (第1次尝试)
2024-01-15 10:30:25 - request_util - INFO - [a1b2c3d4] 请求头: {'User-Agent': 'RPM-AutoTest/1.0', 'Accept': 'application/json', 'Content-Type': 'application/json;charset=UTF-8', 'Authorization': 'Bearer ab***56'}
2024-01-15 10:30:25 - request_util - INFO - [a1b2c3d4] 请求参数: {'json': {'username': 'testuser', 'password': 's***3'}, 'timeout': 30}
2024-01-15 10:30:25 - request_util - INFO - [a1b2c3d4] 请求体(JSON): {'username': 'testuser', 'password': 's***3'}
2024-01-15 10:30:26 - request_util - INFO - [a1b2c3d4] 响应状态: 200, 响应时间: 1250.5ms
2024-01-15 10:30:26 - request_util - INFO - [a1b2c3d4] 响应内容: {"code":"200","message":"登录成功","data":{"token":"xy***89"}}
```
