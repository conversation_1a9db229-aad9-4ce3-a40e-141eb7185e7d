# Python 缓存文件
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.pyc

# 虚拟环境
.env
.venv
venv/
ENV/
env/
*.egg-info/

# pytest 缓存
.pytest_cache/

# 覆盖率报告
htmlcov/
.coverage
.coverage.*

# 日志文件
*.log
logs/

# Allure 报告
allure-results/
allure-results-*/
allure-report/
allure-report-*/

# IDE 配置
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Mac 系统文件
.DS_Store

# 临时文件
temp/
tmp/
*.tmp
*.bak

# 配置文件（包含敏感信息）
config/config.yaml
config/config_dev.yaml
config/config_uat.yaml
config/config_stress.yaml
config/config_prod.yaml

# augment
.augment/

# claude
CLAUDE.md

# lingma
.lingma/

# trae
.trae/

# 但保留脚本文件
!run_stress_test.sh
!send_dingtalk_report.py
