"""
SQLAlchemy数据库访问接口
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from contextlib import contextmanager
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

class ISQLAlchemyUtil(ABC):
    """SQLAlchemy数据库访问接口"""
    
    @abstractmethod
    def execute_query(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询语句"""
        pass
    
    @abstractmethod
    def execute_update(self, sql: str, params: Optional[Dict[str, Any]] = None) -> int:
        """执行更新语句"""
        pass
    
    @abstractmethod
    @contextmanager
    def session_scope(self) -> Session:
        """Session上下文管理器"""
        pass
    
    @abstractmethod
    def get_engine(self) -> Engine:
        """获取SQLAlchemy引擎"""
        pass
    
    @abstractmethod
    def create_all(self) -> None:
        """创建所有表"""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """健康检查"""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """关闭连接"""
        pass