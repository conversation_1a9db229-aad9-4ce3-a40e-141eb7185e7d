"""
项目管理-项目任务及工时相关API封装
"""
import time
import allure
from common.core import Logger
from common.core import ConfigManager
from common.apis import VisitingPlanAPI
from common.apis import ProjectAPI
from collections import Counter

import config

logger = Logger().get_logger()
timestamp = int(time.time() * 1000)

class ProjectAPI:
    """
    项目管理-项目任务及工时API封装类
    """
    
    def __init__(self, request_util):
        self.req = request_util
        self.config = ConfigManager()
        self.project_api = ProjectAPI(request_util)
        self.visitingplan_api = VisitingPlanAPI(request_util)
    
    
    @allure.step("合同管理总工时查询")
    def get_contract_hour(self, contract_id, headers):
        """
        合同管理总工时查询
        以项目编号=cstqlx_0717为例
        """
        
        url = "/api/jeecg-boot/contract/clxmContract/findClxmContractSetp1ById"
        params = {
                "id": contract_id,
                "_t": timestamp
            }
        logger.info(f"开始查询总工时: {params}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"总工时查询为 {response.status_code} - {response.text}小时")
        return response
  

    @allure.step("合同管理--查询报价单元字段数量")
    def get_contract_quotation(self, contract_id, headers):
        """
        存在非“受试者管理”报价单元，项目管理-WBS配置会有数据
        """
        url = "/api/jeecg-boot/contract/clxmContract/findClxmContractQuotationList"
        params = {
                "contractId": contract_id,
                "_t": timestamp
            }
        logger.info(f"开始获取报价单元数量: {params}")
        response = self.req.send_request("GET", url, params=params, headers=headers)

        # 预期的字段数量
        expected_field_count = 2

        # 获取实际返回的字段数量
        actual_field_count = len(response.keys())

        # 判断字段数量是否符合预期
        if actual_field_count == expected_field_count or actual_field_count > expected_field_count:
            logger.info(f"字段数量检查通过: 存在{actual_field_count}个报价单元")
        else:
            logger.info(f"字段数量检查失败: 项目管理_WBS配置无数据,请更换项目")
        return response
    

    @allure.step("访问项目概览-项目工时指标")
    def get_project_hour(self, project_id, headers):
        """
        访问项目概览-项目工时指标
        """
        url = " /rpm-api/project/hourIndexInfo"
        params = {
            "projectId": project_id,
            "_t": timestamp
        }
        logger.info(f"开始访问项目概览-基本信息,项目ID: {params}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"访问基本信息完成: {response.status_code} - {response.text}")
        return response
    

    @allure.step("访问项目概览-月度项目工时指标")
    def get_project_monhour(self, payload, headers):
        """
        访问项目概览-月度项目工时指标
        """
        url = " /rpm-api/project/hourIndexTimeInfo"
        logger.info(f"开始查询月度项目工时指标: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"月度项目工时指标为: {response.status_code} - {response.text}")
        return response
    

    @allure.step("项目执行-查询月计划")
    def get_monhour_info(self, payload, headers):
        """
        项目执行-查询月计划
                payload  =   {
            "endTime": "2025-09-30 23:59:59",
            "projectId": "1945711786514358274",
            "startTime": "2025-09-01 00:00:00"
            }
        """
        
        url = "/rpm-api/task/taskHoursStatistics"
        logger.info(f"查询月计划: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"月计划详情: {response.status_code} - {response.text}")
        return response
    

    @allure.step("项目执行-查询月计划访视任务")
    def get_month_VisitList(self, payload, headers):
        """
        项目执行-查询月计划访视任务
                payload  =   {
            "pageIndex": 1,
            "pageSize": 20,
            "startDate": "2025-09-01 00:00:00",
            "endDate": "2025-09-30 23:59:59",
            "projectId": "1945711786514358274"
            }
        """
        
        url = "/rpm-api/task/visitList"
        logger.info(f"月度访视任务: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"月度访视任务详情: {response.status_code} - {response.text}")
        return response
    

    @allure.step("项目执行-查询月计划非访视任务")
    def get_month_nonVisitList(self, payload, headers):
        """
        项目执行-查询月计划非访视任务
                payload  =   {
            "pageIndex": 1,
            "pageSize": 20,
            "startDate": "2025-09-01 00:00:00",
            "endDate": "2025-09-30 23:59:59",
            "projectId": "1945711786514358274"
            }
        """
        
        url = "/rpm-api/task/nonVisitList"
        logger.info(f"月度非访视任务: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"月度非访视任务详情: {response.status_code} - {response.text}")
        return response
    

    @allure.step("月计划非访视任务-查看详情")
    def month_nonVisitList_info(self, projectId, taskId, headers):
        """
        月计划非访视任务-查看详情
        """
        
        url = "/rpm-api/task/nonVisitTaskInfo"
        params = {
                "taskId": "1962788441642311682",
                "projectId": projectId,
                "_t": timestamp
            }
        logger.info(f"查询非访视任务详情: {params}")
        response = self.req.send_request("GET", url, params=params, headers=headers)
        logger.info(f"非访视任务详情: {response.status_code} - {response.text}")
        return response


    @allure.step("项目执行-查询周计划访视任务")
    def get_week_VisitList(self, payload, headers):
        """
        项目执行-查询周计划访视任务
                payload  =   {
        "pageIndex": 1,
        "pageSize": 20,
        "startDate": "2025-09-08 00:00:00",
        "endDate": "2025-09-14 23:59:59",
        "projectId": "1945711786514358274"
        }
                """
        
        url = "/rpm-api/task/visitList"
        logger.info(f"周访视任务: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"周访视任务详情: {response.status_code} - {response.text}")
        return response
    

    @allure.step("项目执行-查询周计划非访视任务")
    def get_week_nonVisitList(self, payload, headers):
        """
        项目执行-查询周计划非访视任务
                payload  =   {
        "pageIndex": 1,
        "pageSize": 20,
        "startDate": "2025-09-08 00:00:00",
        "endDate": "2025-09-14 23:59:59",
        "projectId": "1945711786514358274"
        }
        """
        
        url = "/rpm-api/task/nonVisitList"
        logger.info(f"周非访视任务: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"周非访视任务详情: {response.status_code} - {response.text}")
        return response


    @allure.step("新增研究中心配置")
    def create_center(self, payload, headers):
        url = "/rpm-api/projectCenter/add"
        """
                payload = {
            "projectId": "1945711786514358274",
            "centerId": "1901903920767315968",
            "centerNo": "0909",
            "centerStandardCode": "13-HLJ-HLJJK",
            "deptId": "1901904071959392258",
            "researcherIds": [
                "1901904179056750595"
            ],
            "isLeader": 1,
            "contractGroupPlanNumber": 5,
            "groupPlanNumber": 5,
            "centerStartTime": "2025-09-30",
            "centerType": 1
            }
        """
        logger.info(f"新增中心: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"新增中心响应: {response.status_code}")
        return response
    

    @allure.step("新增非访视计划")
    def create_NonVisitTaskInfo(self, payload, headers):
        url = "/rpm-api/task/createNonVisitTaskInfo"
        """
                payload = [
        {
            "projectWbsContentId": "1957342616347906050",
            "planWorkHours": 3,
            "finish": 0,
            "priorityCode": "height",
            "planTime": "2025-09-11",
            "projectId": "1945711786514358274",
            "taskType": "nonvisit",
            "wbsContentName": "CR010209启动支持相关其他事项，请在备注说明",
            "wbsContentCode": "CR010209",
            "workClassifyCode": "CR0102",
            "wbsQuoteProjectHours": 10,
            "workContentRemark": null,
            "projectCenterId": "1957343640764715010",
            "centerName": "昆明市第三人民医院 (外科)"
        }
        ]
        """
        logger.info(f"新增非访视任务: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"新增访视任务响应: {response.status_code}")
        return response
    

    @allure.step("切换角色CRC")
    def Switch_Role(self, payload, headers):
        url = "/rpm-api/auth/switchRole"
        """
                payload = {
        "roleId": "1806235754282790916"
        }    
        """
        logger.info(f"切换CRC角色: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"切换成功: {response.status_code}")
        return response
    

    @allure.step("完成非访视计划")
    def Finish_NonVisitTaskInfo(self, payload, headers):
        url = "/rpm-api/task/editVisitTaskInfo"
        """
                payload = {
        "actualCompletionDate": "2025-09-02",
        "actualWorkHours": 3,
        "isComplete": 1,
        "remarks": null,
        "status": "finished",
        "taskId": "1962791232809017345",
        "taskName": "CR010209启动支持相关其他事项，请在备注说明",
        "taskType": "nonvisit"
        }        
        """
        logger.info(f"完成非访视任务: {payload}")
        response = self.req.send_request("POST", url, json=payload, headers=headers)
        logger.info(f"完成访视任务响应: {response.status_code}")
        return response