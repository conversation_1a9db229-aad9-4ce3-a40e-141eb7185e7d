"""
用户模型定义
"""
from sqlalchemy import Column, String, Integer, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import relationship
from models.base import BaseModel

class User(BaseModel):
    """用户模型"""
    
    __tablename__ = 'sys_user'
    
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, nullable=False, comment="邮箱")
    phone = Column(String(20), comment="手机号")
    password_hash = Column(String(255), comment="密码哈希")
    status = Column(String(20), default='active', comment="状态")
    is_active = Column(Boolean, default=True, comment="是否激活")
    description = Column(Text, comment="描述")
    
    # 关系定义
    projects = relationship("Project", back_populates="create_user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"
    
    def is_valid(self) -> bool:
        """检查用户是否有效"""
        return self.is_active and self.status == 'active'
    
    def to_dict(self, include_sensitive=False):
        """转换为字典，可选择是否包含敏感信息"""
        data = super().to_dict()
        if not include_sensitive:
            data.pop('password_hash', None)
        return data