# 项目管理-项目任务及工时测试数据
project_config:
  default_project_id: "1945711786514358274"  # 默认项目ID
  test_projectSimpleName: "cstqlx_0717" # 项目简称
  test_contractNo: "cstqlx_0717" # 项目编号
  contract_id: "A0ECE92F699C4DC6BFE0950295195F4A" # 合同id
  _t: "1756790013"
  
# 用户登录测试数据
login_test:
  valid_credentials:
    description: "有效的登录凭据"
    expected_status: 200
    
  invalid_credentials:
    - username: "invalid_user"
      password: "wrong_password"
      captcha: "wrong_captcha"
      description: "无效用户名和密码"
      expected_status: [400, 401, 403]
    
    - username: ""
      password: ""
      captcha: ""
      description: "空用户名和密码"
      expected_status: [400, 401]

# 项目卡片列表查询测试数据
project_card_list:
  valid_queries:
    - pageIndex: 1
      pageSize: 20
      description: "标准分页查询"
      
    - pageIndex: 1
      pageSize: 10
      description: "小页面查询"
      
    - pageIndex: 2
      pageSize: 15
      description: "第二页查询"
  
  boundary_queries:
    - pageIndex: 1
      pageSize: 1
      description: "最小页大小"
      
    - pageIndex: 1
      pageSize: 100
      description: "较大页大小"
      
    - pageIndex: 0
      pageSize: 20
      description: "页码为0"
      expected_behavior: "可能返回错误或第一页数据"
      
    - pageIndex: 999
      pageSize: 20
      description: "很大的页码"
      expected_behavior: "可能返回空数据"
      
    - pageIndex: -1
      pageSize: 20
      description: "负数页码"
      expected_behavior: "应该返回错误"
      
    - pageIndex: 1
      pageSize: 0
      description: "页大小为0"
      expected_behavior: "应该返回错误"
      
    - pageIndex: 1
      pageSize: -1
      description: "负数页大小"
      expected_behavior: "应该返回错误"

# 项目信息查询测试数据
project_info:
  valid_project_ids:
    - "1945711786514358274"
    
  invalid_project_ids:
    - id: "0"
      description: "ID为0"
      expected_behavior: "可能返回错误或空数据"
      
    - id: "999999999"
      description: "不存在的ID"
      expected_behavior: "应该返回404或空数据"
      
    - id: "invalid_id"
      description: "非数字ID"
      expected_behavior: "应该返回400错误"
      
    - id: ""
      description: "空ID"
      expected_behavior: "应该返回400错误"
      
    - id: "null"
      description: "null字符串"
      expected_behavior: "应该返回400错误"

# 性能测试配置
performance_test:
  card_list_performance:
    test_count: 5
    max_avg_response_time: 5.0  # 秒
    max_single_response_time: 10.0  # 秒
    
  project_info_performance:
    test_count: 3
    max_avg_response_time: 3.0  # 秒
    max_single_response_time: 8.0  # 秒

# 并发测试配置
concurrent_test:
  thread_count: 3
  min_success_rate: 0.8  # 80%
  timeout_per_request: 30  # 秒

# 期望的响应数据校验
response_validation:
  project_card_list:
    expected_response:
      schema:
        code: str
        message: str
        data: [dict, list]  # 可能是字典或列表
      
      business_rules:
        - field: "code"
          operator: "eq"
          value: "200"
          message: "响应码必须为200"
          
        - field: "message"
          operator: "ne"
          value: ""
          message: "消息不能为空"
  
  project_info:
    expected_response:
      schema:
        code: str
        message: str
        data: dict
      
      business_rules:
        - field: "code"
          operator: "eq"
          value: "200"
          message: "响应码必须为200"
          
        - field: "data"
          operator: "ne"
          value: null
          message: "数据不能为空"

# 测试场景配置
test_scenarios:
  smoke_test:
    description: "冒烟测试场景"
    tests:
      - "test_get_project_card_list"
      - "test_get_project_info"
    
  regression_test:
    description: "回归测试场景"
    tests:
      - "test_get_project_card_list"
      - "test_get_project_card_list_pagination"
      - "test_get_project_card_list_boundary"
      - "test_get_project_info"
      - "test_get_project_info_invalid_id"
      - "test_project_hour_complete_flow"
    
  performance_test:
    description: "性能测试场景"
    tests:
      - "test_performance"
      - "test_concurrent_access"
    
  full_test:
    description: "完整测试场景"
    tests:
      - "test_user_login"
      - "test_get_project_card_list"
      - "test_get_project_card_list_pagination"
      - "test_get_project_card_list_boundary"
      - "test_get_project_info"
      - "test_get_project_info_invalid_id"
      - "test_project_hour_complete_flow"
      - "test_concurrent_access"
      - "test_performance"

# 错误处理测试数据
error_handling:
  network_errors:
    - scenario: "连接超时"
      description: "模拟网络连接超时"
      
    - scenario: "服务器错误"
      description: "模拟服务器500错误"
      
    - scenario: "认证失败"
      description: "模拟token过期或无效"

# 数据清理配置
cleanup_config:
  auto_cleanup: true
  cleanup_timeout: 30  # 秒
  cleanup_retry_count: 3
