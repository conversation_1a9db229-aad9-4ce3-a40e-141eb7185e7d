#!/usr/bin/env python3
"""
手动发送钉钉测试报告脚本
用于在自动通知失败时手动发送报告
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.core.config_manager import ConfigManager
from common.core.logger import Logger
from common.integrations.dingtalk_notifier import DingTalkNotifier

def main():
    parser = argparse.ArgumentParser(description="手动发送钉钉测试报告")
    parser.add_argument(
        "--results-dir", 
        default="./allure-results",
        help="Allure结果目录路径 (默认: ./allure-results)"
    )
    parser.add_argument(
        "--webhook",
        help="钉钉webhook地址 (可选，覆盖配置文件)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，不实际发送消息"
    )
    parser.add_argument(
        "--at-all",
        action="store_true",
        help="强制@所有人"
    )
    parser.add_argument(
        "--env",
        help="指定环境 (dev/uat/stress/prod)"
    )
    
    args = parser.parse_args()
    
    # 设置环境变量
    if args.env:
        os.environ["TEST_ENV"] = args.env
    
    # 初始化
    logger = Logger().get_logger()
    config = ConfigManager()
    
    logger.info("=" * 60)
    logger.info("🚀 手动发送钉钉测试报告")
    logger.info("=" * 60)
    
    # 检查结果目录
    results_path = Path(args.results_dir)
    if not results_path.exists():
        logger.error(f"❌ 结果目录不存在: {args.results_dir}")
        return 1
    
    result_files = list(results_path.glob("*-result.json"))
    if not result_files:
        logger.warning(f"⚠️ 结果目录中没有找到测试结果文件: {args.results_dir}")
        logger.info("📁 目录内容:")
        for item in results_path.iterdir():
            logger.info(f"  - {item.name}")
    else:
        logger.info(f"✅ 找到 {len(result_files)} 个测试结果文件")
    
    # 创建钉钉通知器
    try:
        notifier = DingTalkNotifier(config)
        
        # 覆盖webhook（如果指定）
        if args.webhook:
            notifier.webhook_url = args.webhook
            logger.info(f"🔗 使用指定的webhook: {args.webhook[:50]}...")
        
        # 预览模式
        if args.dry_run:
            logger.info("👀 预览模式 - 不会实际发送消息")
            
            if result_files:
                summary = notifier.generate_test_report_summary(str(results_path))
                if summary:
                    markdown_content = notifier.create_dingtalk_markdown_report(summary)
                    logger.info("📝 预览报告内容:")
                    logger.info("-" * 40)
                    print(markdown_content)
                    logger.info("-" * 40)
                else:
                    logger.warning("⚠️ 无法生成测试报告摘要")
            else:
                logger.info("📝 将发送无结果文件的通知")
            
            return 0
        
        # 实际发送
        logger.info("📤 开始发送钉钉通知...")
        
        if args.at_all:
            # 强制@所有人
            success = notifier.send_test_report(str(results_path))
            # 这里可以进一步修改通知器来支持强制@所有人
        else:
            success = notifier.send_test_report(str(results_path))
        
        if success:
            logger.info("✅ 钉钉通知发送成功")
            return 0
        else:
            logger.error("❌ 钉钉通知发送失败")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 发送钉钉通知异常: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
